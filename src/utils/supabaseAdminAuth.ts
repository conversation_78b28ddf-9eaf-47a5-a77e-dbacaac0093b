'use client';

import { supabase, TABLES } from '@/lib/supabase';

// Simple admin authentication for demo purposes
// In production, you'd want proper authentication with JWT, sessions, etc.

const ADMIN_PASSWORD = 'cocktailflow2024'; // In production, this would be hashed and stored securely
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export interface AdminSession {
  isAuthenticated: boolean;
  loginTime: number;
  expiresAt: number;
  sessionToken?: string;
}

/**
 * Generate a secure session token
 */
function generateSessionToken(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 15);
  const moreRandom = Math.random().toString(36).substr(2, 15);
  return `admin-${timestamp}-${random}-${moreRandom}`;
}

/**
 * Get current session token from sessionStorage
 */
function getCurrentSessionToken(): string | null {
  if (typeof window === 'undefined') return null;
  return sessionStorage.getItem('cocktailflow-admin-token');
}

/**
 * Store session token in sessionStorage
 */
function storeSessionToken(token: string): void {
  if (typeof window === 'undefined') return;
  sessionStorage.setItem('cocktailflow-admin-token', token);
}

/**
 * Remove session token from sessionStorage
 */
function removeSessionToken(): void {
  if (typeof window === 'undefined') return;
  sessionStorage.removeItem('cocktailflow-admin-token');
}

/**
 * Check if user is currently authenticated as admin
 */
export async function isAdminAuthenticated(): Promise<boolean> {
  if (typeof window === 'undefined') return false;
  
  try {
    const sessionToken = getCurrentSessionToken();
    if (!sessionToken) return false;
    
    // Check if session exists and is valid in Supabase
    const { data, error } = await supabase
      .from(TABLES.ADMIN_SESSIONS)
      .select('*')
      .eq('session_token', sessionToken)
      .eq('is_authenticated', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Session not found, clean up local storage
        removeSessionToken();
        return false;
      }
      console.error('Error checking admin authentication:', error);
      return false;
    }

    // Check if session is expired
    const expiresAt = new Date(data.expires_at).getTime();
    if (Date.now() > expiresAt) {
      // Session expired, clean up
      await cleanupExpiredSession(sessionToken);
      removeSessionToken();
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error checking admin authentication:', error);
    return false;
  }
}

/**
 * Authenticate admin with password
 */
export async function authenticateAdmin(password: string): Promise<boolean> {
  if (password !== ADMIN_PASSWORD) {
    return false;
  }
  
  try {
    const sessionToken = generateSessionToken();
    const loginTime = new Date();
    const expiresAt = new Date(Date.now() + SESSION_DURATION);
    
    // Store session in Supabase
    const { error } = await supabase
      .from(TABLES.ADMIN_SESSIONS)
      .insert({
        session_token: sessionToken,
        is_authenticated: true,
        login_time: loginTime.toISOString(),
        expires_at: expiresAt.toISOString(),
      });

    if (error) {
      console.error('Error storing admin session:', error);
      return false;
    }

    // Store token locally
    storeSessionToken(sessionToken);
    
    return true;
  } catch (error) {
    console.error('Error authenticating admin:', error);
    return false;
  }
}

/**
 * Logout admin
 */
export async function logoutAdmin(): Promise<void> {
  if (typeof window === 'undefined') return;
  
  try {
    const sessionToken = getCurrentSessionToken();
    if (sessionToken) {
      // Mark session as not authenticated in Supabase
      await supabase
        .from(TABLES.ADMIN_SESSIONS)
        .update({ is_authenticated: false })
        .eq('session_token', sessionToken);
    }
    
    // Remove local token
    removeSessionToken();
  } catch (error) {
    console.error('Error logging out admin:', error);
    // Still remove local token even if Supabase update fails
    removeSessionToken();
  }
}

/**
 * Get admin session info
 */
export async function getAdminSession(): Promise<AdminSession | null> {
  if (typeof window === 'undefined') return null;
  
  try {
    const sessionToken = getCurrentSessionToken();
    if (!sessionToken) return null;
    
    const { data, error } = await supabase
      .from(TABLES.ADMIN_SESSIONS)
      .select('*')
      .eq('session_token', sessionToken)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        removeSessionToken();
        return null;
      }
      console.error('Error getting admin session:', error);
      return null;
    }

    return {
      isAuthenticated: data.is_authenticated,
      loginTime: new Date(data.login_time).getTime(),
      expiresAt: new Date(data.expires_at).getTime(),
      sessionToken: data.session_token,
    };
  } catch (error) {
    console.error('Error getting admin session:', error);
    return null;
  }
}

/**
 * Clean up expired session from database
 */
async function cleanupExpiredSession(sessionToken: string): Promise<void> {
  try {
    await supabase
      .from(TABLES.ADMIN_SESSIONS)
      .delete()
      .eq('session_token', sessionToken);
  } catch (error) {
    console.error('Error cleaning up expired session:', error);
  }
}

/**
 * Clean up all expired sessions (maintenance function)
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const { data, error } = await supabase
      .from(TABLES.ADMIN_SESSIONS)
      .delete()
      .lt('expires_at', new Date().toISOString())
      .select();

    if (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }

    return data?.length || 0;
  } catch (error) {
    console.error('Error cleaning up expired sessions:', error);
    return 0;
  }
}

/**
 * Extend current session (refresh expiration)
 */
export async function extendAdminSession(): Promise<boolean> {
  try {
    const sessionToken = getCurrentSessionToken();
    if (!sessionToken) return false;

    const newExpiresAt = new Date(Date.now() + SESSION_DURATION);

    const { error } = await supabase
      .from(TABLES.ADMIN_SESSIONS)
      .update({ expires_at: newExpiresAt.toISOString() })
      .eq('session_token', sessionToken)
      .eq('is_authenticated', true);

    if (error) {
      console.error('Error extending admin session:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error extending admin session:', error);
    return false;
  }
}
