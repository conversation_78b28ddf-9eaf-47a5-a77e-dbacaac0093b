'use client';

import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { 
  getCocktails,
  getCocktailById,
  createCocktail,
  updateCocktail,
  deleteCocktail,
  getIngredients,
  getIngredientById,
  createIngredient,
  updateIngredient,
  deleteIngredient,
  getGlassTypes,
  getGlassTypeById,
  createGlassType,
  updateGlassType,
  deleteGlassType,
} from '@/lib/supabase';

// Fallback to default data for SSR
import { cocktails as initialCocktails } from '@/data/cocktails';
import { ingredients as initialIngredients, glassTypes as initialGlassTypes } from '@/data/ingredients';

/**
 * Get all cocktails (from Supabase or default data)
 */
export async function getAdminCocktails(): Promise<Cocktail[]> {
  if (typeof window === 'undefined') return initialCocktails;
  
  try {
    return await getCocktails();
  } catch (error) {
    console.error('Error loading admin cocktails from Supabase:', error);
    return initialCocktails;
  }
}

/**
 * Save cocktails to Supabase (not applicable - individual operations used instead)
 * This function is kept for API compatibility but doesn't do bulk saves
 */
export function saveAdminCocktails(cocktails: Cocktail[]): void {
  console.warn('saveAdminCocktails is deprecated with Supabase. Use individual create/update operations.');
}

/**
 * Add a new cocktail
 */
export async function addCocktail(cocktail: Cocktail): Promise<boolean> {
  try {
    await createCocktail(cocktail);
    return true;
  } catch (error) {
    console.error('Error adding cocktail:', error);
    return false;
  }
}

/**
 * Update an existing cocktail
 */
export async function updateCocktailData(id: string, updates: Partial<Cocktail>): Promise<boolean> {
  try {
    await updateCocktail(id, updates);
    return true;
  } catch (error) {
    console.error('Error updating cocktail:', error);
    return false;
  }
}

/**
 * Delete a cocktail
 */
export async function deleteCocktailData(id: string): Promise<boolean> {
  try {
    return await deleteCocktail(id);
  } catch (error) {
    console.error('Error deleting cocktail:', error);
    return false;
  }
}

/**
 * Get cocktail by ID
 */
export async function getCocktailByIdAdmin(id: string): Promise<Cocktail | null> {
  try {
    return await getCocktailById(id);
  } catch (error) {
    console.error('Error getting cocktail by ID:', error);
    return null;
  }
}

/**
 * Get all ingredients (from Supabase or default data)
 */
export async function getAdminIngredients(): Promise<Ingredient[]> {
  if (typeof window === 'undefined') return initialIngredients;
  
  try {
    return await getIngredients();
  } catch (error) {
    console.error('Error loading admin ingredients from Supabase:', error);
    return initialIngredients;
  }
}

/**
 * Save ingredients to Supabase (not applicable - individual operations used instead)
 * This function is kept for API compatibility but doesn't do bulk saves
 */
export function saveAdminIngredients(ingredients: Ingredient[]): void {
  console.warn('saveAdminIngredients is deprecated with Supabase. Use individual create/update operations.');
}

/**
 * Add a new ingredient
 */
export async function addIngredient(ingredient: Ingredient): Promise<boolean> {
  try {
    await createIngredient(ingredient);
    return true;
  } catch (error) {
    console.error('Error adding ingredient:', error);
    return false;
  }
}

/**
 * Update an existing ingredient
 */
export async function updateIngredientData(id: string, updates: Partial<Ingredient>): Promise<boolean> {
  try {
    await updateIngredient(id, updates);
    return true;
  } catch (error) {
    console.error('Error updating ingredient:', error);
    return false;
  }
}

/**
 * Delete an ingredient
 */
export async function deleteIngredientData(id: string): Promise<boolean> {
  try {
    return await deleteIngredient(id);
  } catch (error) {
    console.error('Error deleting ingredient:', error);
    return false;
  }
}

/**
 * Get ingredient by ID
 */
export async function getIngredientByIdAdmin(id: string): Promise<Ingredient | null> {
  try {
    return await getIngredientById(id);
  } catch (error) {
    console.error('Error getting ingredient by ID:', error);
    return null;
  }
}

/**
 * Get all glass types (from Supabase or default data)
 */
export async function getAdminGlassTypes(): Promise<GlassType[]> {
  if (typeof window === 'undefined') return initialGlassTypes;
  
  try {
    return await getGlassTypes();
  } catch (error) {
    console.error('Error loading admin glass types from Supabase:', error);
    return initialGlassTypes;
  }
}

/**
 * Save glass types to Supabase (not applicable - individual operations used instead)
 * This function is kept for API compatibility but doesn't do bulk saves
 */
export function saveAdminGlassTypes(glassTypes: GlassType[]): void {
  console.warn('saveAdminGlassTypes is deprecated with Supabase. Use individual create/update operations.');
}

/**
 * Add a new glass type
 */
export async function addGlassType(glassType: GlassType): Promise<boolean> {
  try {
    await createGlassType(glassType);
    return true;
  } catch (error) {
    console.error('Error adding glass type:', error);
    return false;
  }
}

/**
 * Update an existing glass type
 */
export async function updateGlassTypeData(id: string, updates: Partial<GlassType>): Promise<boolean> {
  try {
    await updateGlassType(id, updates);
    return true;
  } catch (error) {
    console.error('Error updating glass type:', error);
    return false;
  }
}

/**
 * Delete a glass type
 */
export async function deleteGlassTypeData(id: string): Promise<boolean> {
  try {
    return await deleteGlassType(id);
  } catch (error) {
    console.error('Error deleting glass type:', error);
    return false;
  }
}

/**
 * Get glass type by ID
 */
export async function getGlassTypeByIdAdmin(id: string): Promise<GlassType | null> {
  try {
    return await getGlassTypeById(id);
  } catch (error) {
    console.error('Error getting glass type by ID:', error);
    return null;
  }
}

/**
 * Generate a unique ID for new items
 */
export function generateId(prefix: string = ''): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `${prefix}${prefix ? '-' : ''}${timestamp}-${random}`;
}

/**
 * Reset all data to defaults (not applicable with Supabase)
 * This would require deleting all data and re-migrating defaults
 */
export function resetToDefaults(): void {
  console.warn('resetToDefaults is not supported with Supabase. Use the migration utilities instead.');
}
