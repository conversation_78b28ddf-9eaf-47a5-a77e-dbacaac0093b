import { Cocktail, SearchFilters, UserIngredients, CocktailMatch, Ingredient } from '@/types/cocktail';
import { 
  getCocktails,
  getCocktailById,
  searchCocktails as searchSupabaseCocktails,
  getCocktailsByCategory,
  getCocktailsByTag,
  getIngredients,
} from '@/lib/supabase';

// Fallback to default data for SSR
import { cocktails as defaultCocktails } from '@/data/cocktails';
import { ingredients as defaultIngredients } from '@/data/ingredients';

/**
 * Get cocktails (Supabase data if available, otherwise default data)
 */
async function getCocktailData(): Promise<Cocktail[]> {
  if (typeof window === 'undefined') return defaultCocktails;
  
  try {
    return await getCocktails();
  } catch (error) {
    console.warn('Failed to load cocktails from Supabase, using default data');
    return defaultCocktails;
  }
}

/**
 * Get ingredients (Supabase data if available, otherwise default data)
 */
async function getIngredientData(): Promise<Ingredient[]> {
  if (typeof window === 'undefined') return defaultIngredients;
  
  try {
    return await getIngredients();
  } catch (error) {
    console.warn('Failed to load ingredients from Supabase, using default data');
    return defaultIngredients;
  }
}

/**
 * Get cocktail by ID
 */
export async function getCocktailByIdAsync(id: string): Promise<Cocktail | null> {
  if (typeof window === 'undefined') {
    return defaultCocktails.find(c => c.id === id) || null;
  }
  
  try {
    return await getCocktailById(id);
  } catch (error) {
    console.warn('Failed to load cocktail from Supabase, using default data');
    return defaultCocktails.find(c => c.id === id) || null;
  }
}

/**
 * Search cocktails based on filters
 */
export async function searchCocktails(filters: SearchFilters): Promise<Cocktail[]> {
  try {
    let filteredCocktails = await getCocktailData();

    // Filter by search query (name or description)
    if (filters.query) {
      // Use Supabase search if available, otherwise filter locally
      if (typeof window !== 'undefined') {
        try {
          filteredCocktails = await searchSupabaseCocktails(filters.query);
        } catch (error) {
          console.warn('Supabase search failed, using local search');
          const query = filters.query.toLowerCase();
          filteredCocktails = filteredCocktails.filter(cocktail =>
            cocktail.name.toLowerCase().includes(query) ||
            cocktail.description.toLowerCase().includes(query) ||
            cocktail.tags.some(tag => tag.toLowerCase().includes(query))
          );
        }
      } else {
        const query = filters.query.toLowerCase();
        filteredCocktails = filteredCocktails.filter(cocktail =>
          cocktail.name.toLowerCase().includes(query) ||
          cocktail.description.toLowerCase().includes(query) ||
          cocktail.tags.some(tag => tag.toLowerCase().includes(query))
        );
      }
    }

    // Filter by categories
    if (filters.categories && filters.categories.length > 0) {
      filteredCocktails = filteredCocktails.filter(cocktail =>
        filters.categories!.includes(cocktail.category)
      );
    }

    // Filter by tags
    if (filters.tags && filters.tags.length > 0) {
      filteredCocktails = filteredCocktails.filter(cocktail =>
        filters.tags!.some(tag => cocktail.tags.includes(tag))
      );
    }

    // Filter by ingredients
    if (filters.ingredients && filters.ingredients.length > 0) {
      filteredCocktails = filteredCocktails.filter(cocktail =>
        filters.ingredients!.some(ingredientId =>
          cocktail.ingredients.some(ci => ci.ingredient.id === ingredientId)
        )
      );
    }

    // Filter by glass types
    if (filters.glassTypes && filters.glassTypes.length > 0) {
      filteredCocktails = filteredCocktails.filter(cocktail =>
        filters.glassTypes!.includes(cocktail.glassType.id)
      );
    }

    // Filter by difficulty
    if (filters.difficulty && filters.difficulty.length > 0) {
      filteredCocktails = filteredCocktails.filter(cocktail =>
        filters.difficulty!.includes(cocktail.difficulty)
      );
    }

    // Filter by max prep time
    if (filters.maxPrepTime !== undefined) {
      filteredCocktails = filteredCocktails.filter(cocktail =>
        cocktail.prepTime <= filters.maxPrepTime!
      );
    }

    // Filter by alcoholic/non-alcoholic
    if (filters.alcoholic !== undefined) {
      filteredCocktails = filteredCocktails.filter(cocktail => {
        const hasAlcohol = cocktail.ingredients.some(ci => ci.ingredient.alcoholic);
        return filters.alcoholic ? hasAlcohol : !hasAlcohol;
      });
    }

    return filteredCocktails;
  } catch (error) {
    console.error('Error in searchCocktails:', error);
    return [];
  }
}

/**
 * Find cocktails that can be made with available ingredients
 */
export async function findCocktailsWithIngredients(userIngredients: UserIngredients): Promise<CocktailMatch[]> {
  try {
    const allUserIngredients = [
      ...userIngredients.spirits,
      ...userIngredients.mixers,
      ...userIngredients.others
    ];

    const cocktails = await getCocktailData();
    const matches: CocktailMatch[] = cocktails.map(cocktail => {
      const requiredIngredients = cocktail.ingredients.filter(ci => !ci.garnish && !ci.optional);
      const availableIngredients = requiredIngredients.filter(ci =>
        allUserIngredients.includes(ci.ingredient.id)
      );
      
      const missingIngredients = requiredIngredients
        .filter(ci => !allUserIngredients.includes(ci.ingredient.id))
        .map(ci => ci.ingredient);

      const matchPercentage = requiredIngredients.length > 0 
        ? (availableIngredients.length / requiredIngredients.length) * 100 
        : 0;

      const canMake = missingIngredients.length === 0;

      return {
        cocktail,
        matchPercentage,
        missingIngredients,
        canMake
      };
    });

    // Sort by match percentage (highest first)
    return matches.sort((a, b) => b.matchPercentage - a.matchPercentage);
  } catch (error) {
    console.error('Error in findCocktailsWithIngredients:', error);
    return [];
  }
}

/**
 * Get all unique tags from cocktails
 */
export async function getAllTags(): Promise<string[]> {
  try {
    const cocktails = await getCocktailData();
    const allTags = cocktails.flatMap(cocktail => cocktail.tags);
    return [...new Set(allTags)].sort();
  } catch (error) {
    console.error('Error in getAllTags:', error);
    return [];
  }
}

/**
 * Get cocktails by tag
 */
export async function getCocktailsByTagAsync(tag: string): Promise<Cocktail[]> {
  if (typeof window === 'undefined') {
    return defaultCocktails.filter(cocktail => cocktail.tags.includes(tag));
  }
  
  try {
    return await getCocktailsByTag(tag);
  } catch (error) {
    console.warn('Failed to load cocktails by tag from Supabase, using default data');
    return defaultCocktails.filter(cocktail => cocktail.tags.includes(tag));
  }
}

/**
 * Get random cocktails
 */
export async function getRandomCocktails(count: number): Promise<Cocktail[]> {
  try {
    const cocktails = await getCocktailData();
    const shuffled = [...cocktails].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  } catch (error) {
    console.error('Error in getRandomCocktails:', error);
    return [];
  }
}

/**
 * Get featured cocktails (for homepage)
 */
export async function getFeaturedCocktails(): Promise<Cocktail[]> {
  try {
    const cocktails = await getCocktailData();
    const featured = cocktails.filter(cocktail =>
      cocktail.tags.includes('Classic') ||
      cocktail.tags.includes('IBA Official') ||
      cocktail.difficulty === 'easy'
    );
    return featured.slice(0, 6);
  } catch (error) {
    console.error('Error in getFeaturedCocktails:', error);
    return [];
  }
}

/**
 * Get cocktail statistics
 */
export async function getCocktailStats() {
  try {
    const [cocktails, ingredients] = await Promise.all([
      getCocktailData(),
      getIngredientData()
    ]);
    
    const totalCocktails = cocktails.length;
    const totalIngredients = ingredients.length;
    const categories = [...new Set(cocktails.map(c => c.category))].length;
    const avgPrepTime = Math.round(
      cocktails.reduce((sum, c) => sum + c.prepTime, 0) / cocktails.length
    );

    return {
      totalCocktails,
      totalIngredients,
      categories,
      avgPrepTime
    };
  } catch (error) {
    console.error('Error in getCocktailStats:', error);
    return {
      totalCocktails: 0,
      totalIngredients: 0,
      categories: 0,
      avgPrepTime: 0
    };
  }
}
