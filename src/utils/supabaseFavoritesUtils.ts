'use client';

import { 
  getFavoriteIds as getSupabaseFavoriteIds,
  isFavorite as isSupabaseFavorite,
  addFavorite as addSupabaseFavorite,
  removeFavorite as removeSupabaseFavorite,
  toggleFavorite as toggleSupabaseFavorite,
  clearFavorites as clearSupabaseFavorites,
  getFavoritesCount as getSupabaseFavoritesCount,
} from '@/lib/supabase/favorites';

/**
 * Get all favorite cocktail IDs
 * Maintains the same API as the original favoritesUtils
 */
export async function getFavoriteIds(): Promise<string[]> {
  try {
    return await getSupabaseFavoriteIds();
  } catch (error) {
    console.error('Error getting favorite IDs:', error);
    return [];
  }
}

/**
 * Check if a cocktail is favorited
 * Maintains the same API as the original favoritesUtils
 */
export async function isFavorite(cocktailId: string): Promise<boolean> {
  try {
    return await isSupabaseFavorite(cocktailId);
  } catch (error) {
    console.error('Error checking if cocktail is favorite:', error);
    return false;
  }
}

/**
 * Add a cocktail to favorites
 * Maintains the same API as the original favoritesUtils
 */
export async function addFavorite(cocktailId: string): Promise<boolean> {
  try {
    return await addSupabaseFavorite(cocktailId);
  } catch (error) {
    console.error('Error adding favorite:', error);
    return false;
  }
}

/**
 * Remove a cocktail from favorites
 * Maintains the same API as the original favoritesUtils
 */
export async function removeFavorite(cocktailId: string): Promise<boolean> {
  try {
    return await removeSupabaseFavorite(cocktailId);
  } catch (error) {
    console.error('Error removing favorite:', error);
    return false;
  }
}

/**
 * Toggle favorite status for a cocktail
 * Maintains the same API as the original favoritesUtils
 */
export async function toggleFavorite(cocktailId: string): Promise<boolean> {
  try {
    return await toggleSupabaseFavorite(cocktailId);
  } catch (error) {
    console.error('Error toggling favorite:', error);
    return false;
  }
}

/**
 * Clear all favorites
 * Maintains the same API as the original favoritesUtils
 */
export async function clearFavorites(): Promise<boolean> {
  try {
    return await clearSupabaseFavorites();
  } catch (error) {
    console.error('Error clearing favorites:', error);
    return false;
  }
}

/**
 * Get count of favorites
 * New function not in original API
 */
export async function getFavoritesCount(): Promise<number> {
  try {
    return await getSupabaseFavoritesCount();
  } catch (error) {
    console.error('Error getting favorites count:', error);
    return 0;
  }
}

/**
 * Synchronous version for backward compatibility
 * Note: This will return empty array and should be replaced with async version
 */
export function getFavoriteIdsSync(): string[] {
  console.warn('getFavoriteIdsSync is deprecated. Use async getFavoriteIds() instead.');
  return [];
}

/**
 * Synchronous version for backward compatibility
 * Note: This will return false and should be replaced with async version
 */
export function isFavoriteSync(cocktailId: string): boolean {
  console.warn('isFavoriteSync is deprecated. Use async isFavorite() instead.');
  return false;
}

/**
 * Synchronous version for backward compatibility
 * Note: This will do nothing and should be replaced with async version
 */
export function addFavoriteSync(cocktailId: string): void {
  console.warn('addFavoriteSync is deprecated. Use async addFavorite() instead.');
}

/**
 * Synchronous version for backward compatibility
 * Note: This will do nothing and should be replaced with async version
 */
export function removeFavoriteSync(cocktailId: string): void {
  console.warn('removeFavoriteSync is deprecated. Use async removeFavorite() instead.');
}

/**
 * Synchronous version for backward compatibility
 * Note: This will return false and should be replaced with async version
 */
export function toggleFavoriteSync(cocktailId: string): boolean {
  console.warn('toggleFavoriteSync is deprecated. Use async toggleFavorite() instead.');
  return false;
}
