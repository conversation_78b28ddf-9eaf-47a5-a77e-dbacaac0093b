'use client';

import { Cocktail, Ingredient } from '@/types/cocktail';
import { 
  getShoppingList as getSupabaseShoppingList,
  addToShoppingList as addToSupabaseShoppingList,
  removeFromShoppingList as removeFromSupabaseShoppingList,
  updateShoppingListAmount as updateSupabaseShoppingListAmount,
  clearShoppingList as clearSupabaseShoppingList,
  isInShoppingList as isInSupabaseShoppingList,
  getShoppingListCount as getSupabaseShoppingListCount,
  ShoppingListItem,
} from '@/lib/supabase/shoppingList';

// Re-export the ShoppingListItem type for compatibility
export type { ShoppingListItem };

/**
 * Get shopping list
 * Maintains the same API as the original shoppingListUtils
 */
export async function getShoppingList(): Promise<ShoppingListItem[]> {
  try {
    return await getSupabaseShoppingList();
  } catch (error) {
    console.error('Error getting shopping list:', error);
    return [];
  }
}

/**
 * Add an item to the shopping list
 * Maintains the same API as the original shoppingListUtils
 */
export async function addToShoppingList(
  ingredientId: string,
  amount: string,
  cocktailNames: string[] = []
): Promise<boolean> {
  try {
    return await addToSupabaseShoppingList(ingredientId, amount, cocktailNames);
  } catch (error) {
    console.error('Error adding to shopping list:', error);
    return false;
  }
}

/**
 * Remove an item from the shopping list
 * Maintains the same API as the original shoppingListUtils
 */
export async function removeFromShoppingList(ingredientId: string): Promise<boolean> {
  try {
    return await removeFromSupabaseShoppingList(ingredientId);
  } catch (error) {
    console.error('Error removing from shopping list:', error);
    return false;
  }
}

/**
 * Update shopping list item amount
 * Maintains the same API as the original shoppingListUtils
 */
export async function updateShoppingListAmount(
  ingredientId: string,
  amount: string
): Promise<boolean> {
  try {
    return await updateSupabaseShoppingListAmount(ingredientId, amount);
  } catch (error) {
    console.error('Error updating shopping list amount:', error);
    return false;
  }
}

/**
 * Clear the entire shopping list
 * Maintains the same API as the original shoppingListUtils
 */
export async function clearShoppingList(): Promise<boolean> {
  try {
    return await clearSupabaseShoppingList();
  } catch (error) {
    console.error('Error clearing shopping list:', error);
    return false;
  }
}

/**
 * Check if an ingredient is in the shopping list
 * Maintains the same API as the original shoppingListUtils
 */
export async function isInShoppingList(ingredientId: string): Promise<boolean> {
  try {
    return await isInSupabaseShoppingList(ingredientId);
  } catch (error) {
    console.error('Error checking if in shopping list:', error);
    return false;
  }
}

/**
 * Get shopping list count
 * New function not in original API
 */
export async function getShoppingListCount(): Promise<number> {
  try {
    return await getSupabaseShoppingListCount();
  } catch (error) {
    console.error('Error getting shopping list count:', error);
    return 0;
  }
}

/**
 * Add a cocktail's ingredients to the shopping list
 * Enhanced version of the original function
 */
export async function addCocktailToShoppingList(cocktail: Cocktail): Promise<boolean> {
  try {
    let allSuccess = true;

    for (const cocktailIngredient of cocktail.ingredients) {
      if (!cocktailIngredient.garnish) { // Don't add garnishes to shopping list
        const success = await addToShoppingList(
          cocktailIngredient.ingredient.id,
          cocktailIngredient.amount,
          [cocktail.name]
        );
        if (!success) {
          allSuccess = false;
        }
      }
    }

    return allSuccess;
  } catch (error) {
    console.error('Error adding cocktail to shopping list:', error);
    return false;
  }
}

/**
 * Remove a cocktail's ingredients from the shopping list
 * Enhanced version - removes cocktail name from items or removes items entirely
 */
export async function removeCocktailFromShoppingList(cocktail: Cocktail): Promise<boolean> {
  try {
    const shoppingList = await getShoppingList();
    let allSuccess = true;

    for (const cocktailIngredient of cocktail.ingredients) {
      if (!cocktailIngredient.garnish) {
        const existingItem = shoppingList.find(
          item => item.ingredient.id === cocktailIngredient.ingredient.id
        );

        if (existingItem) {
          const updatedCocktails = existingItem.cocktails.filter(name => name !== cocktail.name);
          
          if (updatedCocktails.length === 0) {
            // Remove the item entirely if no cocktails use it
            const success = await removeFromShoppingList(cocktailIngredient.ingredient.id);
            if (!success) {
              allSuccess = false;
            }
          } else {
            // Update the item with remaining cocktails
            // Note: This would require a new function in the Supabase layer
            // For now, we'll just remove the item entirely
            const success = await removeFromShoppingList(cocktailIngredient.ingredient.id);
            if (!success) {
              allSuccess = false;
            }
          }
        }
      }
    }

    return allSuccess;
  } catch (error) {
    console.error('Error removing cocktail from shopping list:', error);
    return false;
  }
}

/**
 * Synchronous version for backward compatibility
 * Note: This will return empty array and should be replaced with async version
 */
export function getShoppingListSync(): ShoppingListItem[] {
  console.warn('getShoppingListSync is deprecated. Use async getShoppingList() instead.');
  return [];
}

/**
 * Synchronous version for backward compatibility
 * Note: This will do nothing and should be replaced with async version
 */
export function addToShoppingListSync(
  ingredientId: string,
  amount: string,
  cocktailNames: string[] = []
): void {
  console.warn('addToShoppingListSync is deprecated. Use async addToShoppingList() instead.');
}

/**
 * Synchronous version for backward compatibility
 * Note: This will do nothing and should be replaced with async version
 */
export function removeFromShoppingListSync(ingredientId: string): void {
  console.warn('removeFromShoppingListSync is deprecated. Use async removeFromShoppingList() instead.');
}
