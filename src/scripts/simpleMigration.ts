import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Import the default data
import { cocktails as defaultCocktails } from '../data/cocktails';
import { ingredients as defaultIngredients, glassTypes as defaultGlassTypes } from '../data/ingredients';

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// LocalStorage keys
const ADMIN_COCKTAILS_KEY = 'cocktailflow-admin-cocktails';
const ADMIN_INGREDIENTS_KEY = 'cocktailflow-admin-ingredients';
const ADMIN_GLASS_TYPES_KEY = 'cocktailflow-admin-glass-types';
const FAVORITES_KEY = 'cocktailflow-favorites';
const SHOPPING_LIST_KEY = 'cocktailflow-shopping-list';

/**
 * Get data from localStorage or use defaults
 */
function getLocalStorageData() {
  if (typeof window === 'undefined') {
    return {
      cocktails: defaultCocktails,
      ingredients: defaultIngredients,
      glassTypes: defaultGlassTypes,
      favorites: [],
      shoppingList: [],
    };
  }

  try {
    const storedCocktails = localStorage.getItem(ADMIN_COCKTAILS_KEY);
    const storedIngredients = localStorage.getItem(ADMIN_INGREDIENTS_KEY);
    const storedGlassTypes = localStorage.getItem(ADMIN_GLASS_TYPES_KEY);
    const storedFavorites = localStorage.getItem(FAVORITES_KEY);
    const storedShoppingList = localStorage.getItem(SHOPPING_LIST_KEY);

    return {
      cocktails: storedCocktails ? JSON.parse(storedCocktails) : defaultCocktails,
      ingredients: storedIngredients ? JSON.parse(storedIngredients) : defaultIngredients,
      glassTypes: storedGlassTypes ? JSON.parse(storedGlassTypes) : defaultGlassTypes,
      favorites: storedFavorites ? JSON.parse(storedFavorites) : [],
      shoppingList: storedShoppingList ? JSON.parse(storedShoppingList) : [],
    };
  } catch (error) {
    console.error('Error reading localStorage data:', error);
    return {
      cocktails: defaultCocktails,
      ingredients: defaultIngredients,
      glassTypes: defaultGlassTypes,
      favorites: [],
      shoppingList: [],
    };
  }
}

/**
 * Check if tables exist and have data
 */
async function checkExistingData(): Promise<boolean> {
  try {
    const { count: ingredientsCount } = await supabase
      .from('ingredients')
      .select('*', { count: 'exact', head: true });
    
    return (ingredientsCount || 0) > 0;
  } catch (error) {
    console.error('Error checking existing data:', error);
    return false;
  }
}

/**
 * Migrate ingredients
 */
async function migrateIngredients(ingredients: any[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${ingredients.length} ingredients...`);

  for (const ingredient of ingredients) {
    try {
      const { error } = await supabase
        .from('ingredients')
        .insert({
          id: ingredient.id,
          name: ingredient.name,
          category: ingredient.category,
          alcoholic: ingredient.alcoholic,
          description: ingredient.description,
          abv: ingredient.abv,
        });

      if (error) {
        throw error;
      }

      migrated++;
      console.log(`✓ Migrated ingredient: ${ingredient.name}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate ingredient ${ingredient.name}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Migrate glass types
 */
async function migrateGlassTypes(glassTypes: any[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${glassTypes.length} glass types...`);

  for (const glassType of glassTypes) {
    try {
      const { error } = await supabase
        .from('glass_types')
        .insert({
          id: glassType.id,
          name: glassType.name,
          description: glassType.description,
          icon_url: glassType.iconUrl,
          capacity: glassType.capacity,
        });

      if (error) {
        throw error;
      }

      migrated++;
      console.log(`✓ Migrated glass type: ${glassType.name}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate glass type ${glassType.name}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Migrate cocktails
 */
async function migrateCocktails(cocktails: any[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${cocktails.length} cocktails...`);

  for (const cocktail of cocktails) {
    try {
      // Insert cocktail
      const { error: cocktailError } = await supabase
        .from('cocktails')
        .insert({
          id: cocktail.id,
          name: cocktail.name,
          description: cocktail.description,
          instructions: cocktail.instructions,
          glass_type_id: cocktail.glassType.id,
          category: cocktail.category,
          tags: cocktail.tags,
          difficulty: cocktail.difficulty,
          prep_time: cocktail.prepTime,
          servings: cocktail.servings,
          image_url: cocktail.imageUrl,
          garnish: cocktail.garnish,
          history: cocktail.history,
          variations: cocktail.variations,
        });

      if (cocktailError) {
        throw cocktailError;
      }

      // Insert cocktail ingredients
      for (const ingredient of cocktail.ingredients) {
        const { error: ingredientError } = await supabase
          .from('cocktail_ingredients')
          .insert({
            cocktail_id: cocktail.id,
            ingredient_id: ingredient.ingredient.id,
            amount: ingredient.amount,
            optional: ingredient.optional || false,
            garnish: ingredient.garnish || false,
          });

        if (ingredientError) {
          console.warn(`Warning: Failed to add ingredient ${ingredient.ingredient.name} to ${cocktail.name}:`, ingredientError);
        }
      }

      migrated++;
      console.log(`✓ Migrated cocktail: ${cocktail.name}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate cocktail ${cocktail.name}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Main migration function
 */
async function runMigration(force: boolean = false): Promise<void> {
  try {
    console.log('🚀 Starting simple migration from LocalStorage to Supabase...');
    console.log(`Force mode: ${force ? 'ON' : 'OFF'}`);
    console.log('');

    // Check if data already exists
    if (!force) {
      const hasExistingData = await checkExistingData();
      if (hasExistingData) {
        console.log('❌ Data already exists in Supabase. Use --force to override.');
        return;
      }
    }

    // Get data from localStorage
    const localData = getLocalStorageData();

    console.log('📊 Data to migrate:');
    console.log(`  Ingredients: ${localData.ingredients.length}`);
    console.log(`  Glass Types: ${localData.glassTypes.length}`);
    console.log(`  Cocktails: ${localData.cocktails.length}`);
    console.log(`  Favorites: ${localData.favorites.length}`);
    console.log(`  Shopping List: ${localData.shoppingList.length}`);
    console.log('');

    // Migrate in order: ingredients -> glass types -> cocktails
    const ingredientsResult = await migrateIngredients(localData.ingredients);
    const glassTypesResult = await migrateGlassTypes(localData.glassTypes);
    const cocktailsResult = await migrateCocktails(localData.cocktails);

    const totalMigrated = 
      ingredientsResult.migrated + 
      glassTypesResult.migrated + 
      cocktailsResult.migrated;

    const totalErrors = 
      ingredientsResult.errors + 
      glassTypesResult.errors + 
      cocktailsResult.errors;

    console.log('');
    console.log('📊 Migration Results:');
    console.log('===================');
    console.log(`Status: ${totalErrors === 0 ? '✅ SUCCESS' : '⚠️  PARTIAL SUCCESS'}`);
    console.log(`Total migrated: ${totalMigrated} items`);
    console.log(`Total errors: ${totalErrors}`);
    console.log('');
    console.log('Details:');
    console.log(`  Ingredients: ${ingredientsResult.migrated} migrated, ${ingredientsResult.errors} errors`);
    console.log(`  Glass Types: ${glassTypesResult.migrated} migrated, ${glassTypesResult.errors} errors`);
    console.log(`  Cocktails: ${cocktailsResult.migrated} migrated, ${cocktailsResult.errors} errors`);

    if (totalErrors === 0) {
      console.log('');
      console.log('🎉 Migration completed successfully!');
    } else {
      console.log('');
      console.log('⚠️  Migration completed with some errors. Check the logs above.');
    }

  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const force = args.includes('--force');

  await runMigration(force);
}

// Run the migration
main().catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
