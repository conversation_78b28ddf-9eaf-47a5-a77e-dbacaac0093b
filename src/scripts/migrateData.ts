import dotenv from 'dotenv';
import { migrateLocalStorageToSupabase } from '../lib/migration/dataMigration';

// Load environment variables
dotenv.config({ path: '.env.local' });

/**
 * Command line script to migrate data from LocalStorage to Supabase
 * Usage: npx ts-node src/scripts/migrateData.ts [--force]
 */
async function main() {
  const args = process.argv.slice(2);
  const force = args.includes('--force');

  console.log('🚀 Starting data migration from LocalStorage to Supabase...');
  console.log(`Force mode: ${force ? 'ON' : 'OFF'}`);
  console.log('');

  try {
    const result = await migrateLocalStorageToSupabase(force);

    console.log('');
    console.log('📊 Migration Results:');
    console.log('===================');
    console.log(`Status: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`Message: ${result.message}`);
    console.log('');
    console.log('Details:');
    console.log(`  Ingredients: ${result.details.ingredients.migrated} migrated, ${result.details.ingredients.errors} errors`);
    console.log(`  Glass Types: ${result.details.glassTypes.migrated} migrated, ${result.details.glassTypes.errors} errors`);
    console.log(`  Cocktails: ${result.details.cocktails.migrated} migrated, ${result.details.cocktails.errors} errors`);
    console.log(`  Favorites: ${result.details.favorites.migrated} migrated, ${result.details.favorites.errors} errors`);
    console.log(`  Shopping List: ${result.details.shoppingList.migrated} migrated, ${result.details.shoppingList.errors} errors`);

    if (result.success) {
      console.log('');
      console.log('🎉 Migration completed successfully!');
      console.log('Your data has been migrated to Supabase.');
      console.log('You can now update your application to use Supabase instead of LocalStorage.');
      process.exit(0);
    } else {
      console.log('');
      console.log('⚠️  Migration completed with errors.');
      console.log('Please check the logs above for details.');
      process.exit(1);
    }
  } catch (error) {
    console.error('');
    console.error('💥 Migration failed with an unexpected error:');
    console.error(error);
    process.exit(1);
  }
}

// Run the migration
main().catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
