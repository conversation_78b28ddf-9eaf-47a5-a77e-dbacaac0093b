import dotenv from 'dotenv';
import { supabase } from '../lib/supabase';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config({ path: '.env.local' });

/**
 * Setup the database schema by running the migration SQL
 * This script should be run once to initialize the database
 */
export async function setupDatabase() {
  try {
    console.log('Setting up database schema...');
    
    // Read the migration SQL file
    const migrationPath = path.join(process.cwd(), 'supabase', 'migrations', '001_initial_schema.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`Executing ${statements.length} SQL statements...`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        console.log(`Executing statement ${i + 1}/${statements.length}...`);
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          console.error('Statement:', statement);
          throw error;
        }
      }
    }
    
    console.log('Database schema setup completed successfully!');
    return true;
  } catch (error) {
    console.error('Error setting up database:', error);
    return false;
  }
}

/**
 * Alternative method using direct SQL execution
 * This requires the SQL to be executed manually in Supabase dashboard
 */
export function getDatabaseSetupSQL(): string {
  const migrationPath = path.join(process.cwd(), 'supabase', 'migrations', '001_initial_schema.sql');
  return fs.readFileSync(migrationPath, 'utf8');
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupDatabase()
    .then((success) => {
      if (success) {
        console.log('✅ Database setup completed');
        process.exit(0);
      } else {
        console.log('❌ Database setup failed');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('❌ Database setup failed:', error);
      process.exit(1);
    });
}
