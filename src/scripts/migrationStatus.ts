import dotenv from 'dotenv';
import { supabase } from '@/lib/supabase';
import { getCacheStatus } from '@/lib/offline/cacheManager';
import { getCurrentDataSource, getMigrationStatus } from '@/config/dataSource';

// Load environment variables
dotenv.config({ path: '.env.local' });

/**
 * Check Supabase connection and basic functionality
 */
async function checkSupabaseConnection(): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('ingredients')
      .select('count')
      .limit(1);
    
    return !error;
  } catch (error) {
    return false;
  }
}

/**
 * Get table counts from Supabase
 */
async function getTableCounts() {
  try {
    const [cocktails, ingredients, glassTypes, favorites, shoppingList] = await Promise.allSettled([
      supabase.from('cocktails').select('*', { count: 'exact', head: true }),
      supabase.from('ingredients').select('*', { count: 'exact', head: true }),
      supabase.from('glass_types').select('*', { count: 'exact', head: true }),
      supabase.from('favorites').select('*', { count: 'exact', head: true }),
      supabase.from('shopping_list').select('*', { count: 'exact', head: true }),
    ]);

    return {
      cocktails: cocktails.status === 'fulfilled' ? cocktails.value.count || 0 : 'Error',
      ingredients: ingredients.status === 'fulfilled' ? ingredients.value.count || 0 : 'Error',
      glassTypes: glassTypes.status === 'fulfilled' ? glassTypes.value.count || 0 : 'Error',
      favorites: favorites.status === 'fulfilled' ? favorites.value.count || 0 : 'Error',
      shoppingList: shoppingList.status === 'fulfilled' ? shoppingList.value.count || 0 : 'Error',
    };
  } catch (error) {
    return {
      cocktails: 'Error',
      ingredients: 'Error',
      glassTypes: 'Error',
      favorites: 'Error',
      shoppingList: 'Error',
    };
  }
}

/**
 * Check LocalStorage data
 */
function getLocalStorageData() {
  if (typeof window === 'undefined') {
    return {
      cocktails: 'N/A (Server)',
      ingredients: 'N/A (Server)',
      glassTypes: 'N/A (Server)',
      favorites: 'N/A (Server)',
      shoppingList: 'N/A (Server)',
    };
  }

  try {
    const cocktails = localStorage.getItem('cocktailflow-admin-cocktails');
    const ingredients = localStorage.getItem('cocktailflow-admin-ingredients');
    const glassTypes = localStorage.getItem('cocktailflow-admin-glass-types');
    const favorites = localStorage.getItem('cocktailflow-favorites');
    const shoppingList = localStorage.getItem('cocktailflow-shopping-list');

    return {
      cocktails: cocktails ? JSON.parse(cocktails).length : 0,
      ingredients: ingredients ? JSON.parse(ingredients).length : 0,
      glassTypes: glassTypes ? JSON.parse(glassTypes).length : 0,
      favorites: favorites ? JSON.parse(favorites).length : 0,
      shoppingList: shoppingList ? JSON.parse(shoppingList).length : 0,
    };
  } catch (error) {
    return {
      cocktails: 'Error',
      ingredients: 'Error',
      glassTypes: 'Error',
      favorites: 'Error',
      shoppingList: 'Error',
    };
  }
}

/**
 * Print migration status report
 */
export async function printMigrationStatus(): Promise<void> {
  console.log('🔍 CocktailFlow Migration Status Report');
  console.log('=' .repeat(50));

  // Basic configuration
  const dataSource = getCurrentDataSource();
  const migrationStatus = getMigrationStatus();
  const isSupabaseConnected = await checkSupabaseConnection();

  console.log('\n📋 Configuration:');
  console.log(`   Current Data Source: ${dataSource}`);
  console.log(`   Supabase Connection: ${isSupabaseConnected ? '✅ Connected' : '❌ Failed'}`);
  
  if (migrationStatus) {
    console.log(`   Migration Status: ${migrationStatus.completed ? '✅ Completed' : '⏳ Pending'}`);
    console.log(`   Migration Date: ${new Date(migrationStatus.timestamp).toLocaleString()}`);
  } else {
    console.log('   Migration Status: ❓ Unknown');
  }

  // Data counts
  console.log('\n📊 Data Counts:');
  
  if (dataSource === 'supabase' && isSupabaseConnected) {
    console.log('   Supabase Data:');
    const supabaseCounts = await getTableCounts();
    console.log(`     Cocktails: ${supabaseCounts.cocktails}`);
    console.log(`     Ingredients: ${supabaseCounts.ingredients}`);
    console.log(`     Glass Types: ${supabaseCounts.glassTypes}`);
    console.log(`     Favorites: ${supabaseCounts.favorites}`);
    console.log(`     Shopping List: ${supabaseCounts.shoppingList}`);
  }

  console.log('   LocalStorage Data:');
  const localCounts = getLocalStorageData();
  console.log(`     Cocktails: ${localCounts.cocktails}`);
  console.log(`     Ingredients: ${localCounts.ingredients}`);
  console.log(`     Glass Types: ${localCounts.glassTypes}`);
  console.log(`     Favorites: ${localCounts.favorites}`);
  console.log(`     Shopping List: ${localCounts.shoppingList}`);

  // Cache status
  if (typeof window !== 'undefined') {
    console.log('\n💾 Cache Status:');
    const cacheStatus = getCacheStatus();
    console.log(`   Online: ${cacheStatus.isOnline ? '✅ Yes' : '❌ No'}`);
    console.log(`   Has Valid Cache: ${cacheStatus.hasValidCache ? '✅ Yes' : '❌ No'}`);
    console.log(`   Cache Size: ${(cacheStatus.cacheSize / 1024).toFixed(2)} KB`);
    if (cacheStatus.lastSync) {
      console.log(`   Last Sync: ${new Date(cacheStatus.lastSync).toLocaleString()}`);
    }
  }

  // Recommendations
  console.log('\n💡 Recommendations:');
  
  if (dataSource === 'localStorage') {
    console.log('   • Consider migrating to Supabase for better data persistence');
    console.log('   • Run: npx ts-node src/scripts/migrateData.ts');
  } else if (dataSource === 'supabase') {
    if (!isSupabaseConnected) {
      console.log('   ⚠️  Supabase connection failed - check your configuration');
      console.log('   • Verify NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY');
      console.log('   • Check your internet connection');
    } else {
      console.log('   ✅ Migration appears successful!');
      console.log('   • Run tests: npx ts-node src/scripts/testMigration.ts');
    }
  }

  if (typeof window !== 'undefined') {
    const cacheStatus = getCacheStatus();
    if (!cacheStatus.hasValidCache && dataSource === 'supabase') {
      console.log('   • Consider warming up the cache by browsing the app');
    }
  }

  console.log('\n🔗 Useful Commands:');
  console.log('   • Setup database: npx ts-node src/scripts/setupDatabase.ts');
  console.log('   • Migrate data: npx ts-node src/scripts/migrateData.ts');
  console.log('   • Run tests: npx ts-node src/scripts/testMigration.ts');
  console.log('   • Check status: npx ts-node src/scripts/migrationStatus.ts');

  console.log('\n📚 Documentation: See DATABASE_SETUP.md for detailed instructions');
  console.log('=' .repeat(50));
}

/**
 * Main function
 */
async function main() {
  try {
    await printMigrationStatus();
  } catch (error) {
    console.error('Error checking migration status:', error);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (require.main === module) {
  main();
}
