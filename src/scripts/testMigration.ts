import dotenv from 'dotenv';
import {
  getCocktails,
  getIngredients,
  getGlassTypes,
  getCocktailById,
  createCocktail,
  updateCocktail,
  deleteCocktail,
  getFavoriteIds,
  addFavorite,
  removeFavorite,
  getShoppingList,
  addToShoppingList,
  removeFromShoppingList,
} from '@/lib/supabase';
import { migrateLocalStorageToSupabase } from '@/lib/migration/dataMigration';
import { Cocktail, Ingredient, GlassType, CocktailCategory, Difficulty } from '@/types/cocktail';

// Load environment variables
dotenv.config({ path: '.env.local' });

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

interface TestSuite {
  name: string;
  results: TestResult[];
  totalPassed: number;
  totalFailed: number;
  totalDuration: number;
}

/**
 * Run a single test with timing
 */
async function runTest(name: string, testFn: () => Promise<void>): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    await testFn();
    const duration = Date.now() - startTime;
    console.log(`✅ ${name} (${duration}ms)`);
    return { name, passed: true, duration };
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`❌ ${name} (${duration}ms): ${errorMessage}`);
    return { name, passed: false, error: errorMessage, duration };
  }
}

/**
 * Test basic CRUD operations for cocktails
 */
async function testCocktailCRUD(): Promise<TestResult[]> {
  const tests: TestResult[] = [];
  
  // Test data
  const testCocktail: Cocktail = {
    id: 'test-cocktail-' + Date.now(),
    name: 'Test Cocktail',
    description: 'A test cocktail for migration testing',
    instructions: ['Mix ingredients', 'Serve'],
    ingredients: [],
    glassType: { id: 'highball', name: 'Highball', description: 'Tall glass' },
    category: CocktailCategory.MODERN,
    tags: ['Test'],
    difficulty: Difficulty.EASY,
    prepTime: 5,
    servings: 1,
  };

  // Test Create
  tests.push(await runTest('Create Cocktail', async () => {
    const created = await createCocktail(testCocktail);
    if (!created || created.id !== testCocktail.id) {
      throw new Error('Failed to create cocktail');
    }
  }));

  // Test Read
  tests.push(await runTest('Read Cocktail', async () => {
    const cocktail = await getCocktailById(testCocktail.id);
    if (!cocktail || cocktail.name !== testCocktail.name) {
      throw new Error('Failed to read cocktail');
    }
  }));

  // Test Update
  tests.push(await runTest('Update Cocktail', async () => {
    const updated = await updateCocktail(testCocktail.id, { 
      name: 'Updated Test Cocktail' 
    });
    if (!updated || updated.name !== 'Updated Test Cocktail') {
      throw new Error('Failed to update cocktail');
    }
  }));

  // Test Delete
  tests.push(await runTest('Delete Cocktail', async () => {
    const deleted = await deleteCocktail(testCocktail.id);
    if (!deleted) {
      throw new Error('Failed to delete cocktail');
    }
    
    // Verify deletion
    const cocktail = await getCocktailById(testCocktail.id);
    if (cocktail) {
      throw new Error('Cocktail still exists after deletion');
    }
  }));

  return tests;
}

/**
 * Test favorites functionality
 */
async function testFavorites(): Promise<TestResult[]> {
  const tests: TestResult[] = [];
  const testCocktailId = 'gin-tonic'; // Assuming this exists

  // Test Add Favorite
  tests.push(await runTest('Add Favorite', async () => {
    const success = await addFavorite(testCocktailId);
    if (!success) {
      throw new Error('Failed to add favorite');
    }
  }));

  // Test Get Favorites
  tests.push(await runTest('Get Favorites', async () => {
    const favorites = await getFavoriteIds();
    if (!favorites.includes(testCocktailId)) {
      throw new Error('Favorite not found in list');
    }
  }));

  // Test Remove Favorite
  tests.push(await runTest('Remove Favorite', async () => {
    const success = await removeFavorite(testCocktailId);
    if (!success) {
      throw new Error('Failed to remove favorite');
    }
    
    // Verify removal
    const favorites = await getFavoriteIds();
    if (favorites.includes(testCocktailId)) {
      throw new Error('Favorite still exists after removal');
    }
  }));

  return tests;
}

/**
 * Test shopping list functionality
 */
async function testShoppingList(): Promise<TestResult[]> {
  const tests: TestResult[] = [];
  const testIngredientId = 'gin'; // Assuming this exists

  // Test Add to Shopping List
  tests.push(await runTest('Add to Shopping List', async () => {
    const success = await addToShoppingList(testIngredientId, '1 bottle', ['Test Cocktail']);
    if (!success) {
      throw new Error('Failed to add to shopping list');
    }
  }));

  // Test Get Shopping List
  tests.push(await runTest('Get Shopping List', async () => {
    const shoppingList = await getShoppingList();
    const item = shoppingList.find(item => item.ingredient.id === testIngredientId);
    if (!item) {
      throw new Error('Shopping list item not found');
    }
  }));

  // Test Remove from Shopping List
  tests.push(await runTest('Remove from Shopping List', async () => {
    const success = await removeFromShoppingList(testIngredientId);
    if (!success) {
      throw new Error('Failed to remove from shopping list');
    }
    
    // Verify removal
    const shoppingList = await getShoppingList();
    const item = shoppingList.find(item => item.ingredient.id === testIngredientId);
    if (item) {
      throw new Error('Shopping list item still exists after removal');
    }
  }));

  return tests;
}

/**
 * Test data integrity after migration
 */
async function testDataIntegrity(): Promise<TestResult[]> {
  const tests: TestResult[] = [];

  // Test that we have data
  tests.push(await runTest('Check Cocktails Exist', async () => {
    const cocktails = await getCocktails();
    if (cocktails.length === 0) {
      throw new Error('No cocktails found after migration');
    }
  }));

  tests.push(await runTest('Check Ingredients Exist', async () => {
    const ingredients = await getIngredients();
    if (ingredients.length === 0) {
      throw new Error('No ingredients found after migration');
    }
  }));

  tests.push(await runTest('Check Glass Types Exist', async () => {
    const glassTypes = await getGlassTypes();
    if (glassTypes.length === 0) {
      throw new Error('No glass types found after migration');
    }
  }));

  // Test data relationships
  tests.push(await runTest('Check Cocktail Relationships', async () => {
    const cocktails = await getCocktails();
    const firstCocktail = cocktails[0];
    
    if (!firstCocktail.glassType) {
      throw new Error('Cocktail missing glass type relationship');
    }
    
    if (!firstCocktail.ingredients || firstCocktail.ingredients.length === 0) {
      throw new Error('Cocktail missing ingredients relationship');
    }
  }));

  return tests;
}

/**
 * Run all migration tests
 */
export async function runMigrationTests(): Promise<TestSuite[]> {
  console.log('🧪 Starting Migration Tests...\n');
  
  const testSuites: TestSuite[] = [];
  
  // Run test suites
  const suites = [
    { name: 'Data Integrity', testFn: testDataIntegrity },
    { name: 'Cocktail CRUD', testFn: testCocktailCRUD },
    { name: 'Favorites', testFn: testFavorites },
    { name: 'Shopping List', testFn: testShoppingList },
  ];
  
  for (const suite of suites) {
    console.log(`\n📋 Running ${suite.name} Tests:`);
    const startTime = Date.now();
    
    try {
      const results = await suite.testFn();
      const totalDuration = Date.now() - startTime;
      const totalPassed = results.filter(r => r.passed).length;
      const totalFailed = results.filter(r => !r.passed).length;
      
      testSuites.push({
        name: suite.name,
        results,
        totalPassed,
        totalFailed,
        totalDuration,
      });
      
      console.log(`\n${suite.name} Summary: ${totalPassed} passed, ${totalFailed} failed (${totalDuration}ms)`);
    } catch (error) {
      console.error(`\n❌ ${suite.name} suite failed:`, error);
      testSuites.push({
        name: suite.name,
        results: [{ 
          name: 'Suite Error', 
          passed: false, 
          error: error instanceof Error ? error.message : String(error),
          duration: Date.now() - startTime 
        }],
        totalPassed: 0,
        totalFailed: 1,
        totalDuration: Date.now() - startTime,
      });
    }
  }
  
  return testSuites;
}

/**
 * Print test summary
 */
export function printTestSummary(testSuites: TestSuite[]): void {
  console.log('\n' + '='.repeat(50));
  console.log('📊 MIGRATION TEST SUMMARY');
  console.log('='.repeat(50));
  
  let totalPassed = 0;
  let totalFailed = 0;
  let totalDuration = 0;
  
  testSuites.forEach(suite => {
    totalPassed += suite.totalPassed;
    totalFailed += suite.totalFailed;
    totalDuration += suite.totalDuration;
    
    const status = suite.totalFailed === 0 ? '✅' : '❌';
    console.log(`${status} ${suite.name}: ${suite.totalPassed}/${suite.totalPassed + suite.totalFailed} passed`);
  });
  
  console.log('\n' + '-'.repeat(30));
  console.log(`Total: ${totalPassed} passed, ${totalFailed} failed`);
  console.log(`Duration: ${totalDuration}ms`);
  console.log(`Success Rate: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
  
  if (totalFailed === 0) {
    console.log('\n🎉 All tests passed! Migration is successful.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
  }
}

/**
 * Main test runner
 */
async function main() {
  try {
    const testSuites = await runMigrationTests();
    printTestSummary(testSuites);
    
    const totalFailed = testSuites.reduce((sum, suite) => sum + suite.totalFailed, 0);
    process.exit(totalFailed === 0 ? 0 : 1);
  } catch (error) {
    console.error('Test runner failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  main();
}
