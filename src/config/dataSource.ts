/**
 * Data source configuration
 * This file allows easy switching between LocalStorage and Supabase implementations
 */

// Environment variable to control data source
const USE_SUPABASE = process.env.NEXT_PUBLIC_USE_SUPABASE === 'true';

// Export the configuration
export const DATA_SOURCE_CONFIG = {
  useSupabase: USE_SUPABASE,
  fallbackToLocalStorage: true, // Whether to fallback to LocalStorage if Supabase fails
} as const;

// Conditional exports based on configuration
export const cocktailUtils = USE_SUPABASE 
  ? import('../utils/supabaseCocktailUtils')
  : import('../utils/cocktailUtils');

export const adminDataUtils = USE_SUPABASE
  ? import('../utils/supabaseAdminDataUtils')
  : import('../utils/adminDataUtils');

export const favoritesUtils = USE_SUPABASE
  ? import('../utils/supabaseFavoritesUtils')
  : import('../utils/favoritesUtils');

export const shoppingListUtils = USE_SUPABASE
  ? import('../utils/supabaseShoppingListUtils')
  : import('../utils/shoppingListUtils');

export const adminAuth = USE_SUPABASE
  ? import('../utils/supabaseAdminAuth')
  : import('../utils/adminAuth');

/**
 * Helper function to get the current data source
 */
export function getCurrentDataSource(): 'supabase' | 'localStorage' {
  return USE_SUPABASE ? 'supabase' : 'localStorage';
}

/**
 * Helper function to check if Supabase is enabled
 */
export function isSupabaseEnabled(): boolean {
  return USE_SUPABASE;
}

/**
 * Migration status tracking
 */
export const MIGRATION_STATUS_KEY = 'cocktailflow-migration-status';

export interface MigrationStatus {
  completed: boolean;
  timestamp: number;
  version: string;
  dataSource: 'supabase' | 'localStorage';
}

/**
 * Get migration status from localStorage
 */
export function getMigrationStatus(): MigrationStatus | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const status = localStorage.getItem(MIGRATION_STATUS_KEY);
    return status ? JSON.parse(status) : null;
  } catch (error) {
    console.error('Error reading migration status:', error);
    return null;
  }
}

/**
 * Set migration status in localStorage
 */
export function setMigrationStatus(status: MigrationStatus): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(MIGRATION_STATUS_KEY, JSON.stringify(status));
  } catch (error) {
    console.error('Error saving migration status:', error);
  }
}

/**
 * Check if migration is needed
 */
export function isMigrationNeeded(): boolean {
  if (!USE_SUPABASE) return false;
  
  const status = getMigrationStatus();
  return !status || !status.completed || status.dataSource !== 'supabase';
}

/**
 * Mark migration as completed
 */
export function markMigrationCompleted(): void {
  setMigrationStatus({
    completed: true,
    timestamp: Date.now(),
    version: '1.0.0',
    dataSource: 'supabase',
  });
}
