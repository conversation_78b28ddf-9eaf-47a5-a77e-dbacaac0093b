import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { 
  createIngredient, 
  createGlassType, 
  createCocktail,
  addFavorite,
  addToShoppingList,
  getIngredients,
  getGlassTypes,
  getCocktails
} from '@/lib/supabase';

// Import the default data
import { cocktails as defaultCocktails } from '@/data/cocktails';
import { ingredients as defaultIngredients, glassTypes as defaultGlassTypes } from '@/data/ingredients';

// LocalStorage keys (matching the existing ones)
const ADMIN_COCKTAILS_KEY = 'cocktailflow-admin-cocktails';
const ADMIN_INGREDIENTS_KEY = 'cocktailflow-admin-ingredients';
const ADMIN_GLASS_TYPES_KEY = 'cocktailflow-admin-glass-types';
const FAVORITES_KEY = 'cocktailflow-favorites';
const SHOPPING_LIST_KEY = 'cocktailflow-shopping-list';

export interface MigrationResult {
  success: boolean;
  message: string;
  details: {
    ingredients: { migrated: number; errors: number };
    glassTypes: { migrated: number; errors: number };
    cocktails: { migrated: number; errors: number };
    favorites: { migrated: number; errors: number };
    shoppingList: { migrated: number; errors: number };
  };
}

/**
 * Get data from localStorage or use defaults
 */
function getLocalStorageData() {
  if (typeof window === 'undefined') {
    return {
      cocktails: defaultCocktails,
      ingredients: defaultIngredients,
      glassTypes: defaultGlassTypes,
      favorites: [],
      shoppingList: [],
    };
  }

  try {
    const storedCocktails = localStorage.getItem(ADMIN_COCKTAILS_KEY);
    const storedIngredients = localStorage.getItem(ADMIN_INGREDIENTS_KEY);
    const storedGlassTypes = localStorage.getItem(ADMIN_GLASS_TYPES_KEY);
    const storedFavorites = localStorage.getItem(FAVORITES_KEY);
    const storedShoppingList = localStorage.getItem(SHOPPING_LIST_KEY);

    return {
      cocktails: storedCocktails ? JSON.parse(storedCocktails) : defaultCocktails,
      ingredients: storedIngredients ? JSON.parse(storedIngredients) : defaultIngredients,
      glassTypes: storedGlassTypes ? JSON.parse(storedGlassTypes) : defaultGlassTypes,
      favorites: storedFavorites ? JSON.parse(storedFavorites) : [],
      shoppingList: storedShoppingList ? JSON.parse(storedShoppingList) : [],
    };
  } catch (error) {
    console.error('Error reading localStorage data:', error);
    return {
      cocktails: defaultCocktails,
      ingredients: defaultIngredients,
      glassTypes: defaultGlassTypes,
      favorites: [],
      shoppingList: [],
    };
  }
}

/**
 * Migrate ingredients to Supabase
 */
async function migrateIngredients(ingredients: Ingredient[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${ingredients.length} ingredients...`);

  for (const ingredient of ingredients) {
    try {
      await createIngredient(ingredient);
      migrated++;
      console.log(`✓ Migrated ingredient: ${ingredient.name}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate ingredient ${ingredient.name}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Migrate glass types to Supabase
 */
async function migrateGlassTypes(glassTypes: GlassType[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${glassTypes.length} glass types...`);

  for (const glassType of glassTypes) {
    try {
      await createGlassType(glassType);
      migrated++;
      console.log(`✓ Migrated glass type: ${glassType.name}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate glass type ${glassType.name}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Migrate cocktails to Supabase
 */
async function migrateCocktails(cocktails: Cocktail[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${cocktails.length} cocktails...`);

  for (const cocktail of cocktails) {
    try {
      await createCocktail(cocktail);
      migrated++;
      console.log(`✓ Migrated cocktail: ${cocktail.name}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate cocktail ${cocktail.name}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Migrate favorites to Supabase
 */
async function migrateFavorites(favoriteIds: string[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${favoriteIds.length} favorites...`);

  for (const cocktailId of favoriteIds) {
    try {
      await addFavorite(cocktailId);
      migrated++;
      console.log(`✓ Migrated favorite: ${cocktailId}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate favorite ${cocktailId}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Migrate shopping list to Supabase
 */
async function migrateShoppingList(shoppingList: any[]): Promise<{ migrated: number; errors: number }> {
  let migrated = 0;
  let errors = 0;

  console.log(`Migrating ${shoppingList.length} shopping list items...`);

  for (const item of shoppingList) {
    try {
      await addToShoppingList(
        item.ingredient.id,
        item.amount,
        item.cocktails || []
      );
      migrated++;
      console.log(`✓ Migrated shopping list item: ${item.ingredient.name}`);
    } catch (error) {
      errors++;
      console.error(`✗ Failed to migrate shopping list item ${item.ingredient?.name}:`, error);
    }
  }

  return { migrated, errors };
}

/**
 * Check if data already exists in Supabase
 */
async function checkExistingData(): Promise<boolean> {
  try {
    const [ingredients, glassTypes, cocktails] = await Promise.all([
      getIngredients(),
      getGlassTypes(),
      getCocktails(),
    ]);

    return ingredients.length > 0 || glassTypes.length > 0 || cocktails.length > 0;
  } catch (error) {
    console.error('Error checking existing data:', error);
    return false;
  }
}

/**
 * Main migration function
 */
export async function migrateLocalStorageToSupabase(force: boolean = false): Promise<MigrationResult> {
  try {
    console.log('Starting migration from LocalStorage to Supabase...');

    // Check if data already exists
    if (!force) {
      const hasExistingData = await checkExistingData();
      if (hasExistingData) {
        return {
          success: false,
          message: 'Data already exists in Supabase. Use force=true to override.',
          details: {
            ingredients: { migrated: 0, errors: 0 },
            glassTypes: { migrated: 0, errors: 0 },
            cocktails: { migrated: 0, errors: 0 },
            favorites: { migrated: 0, errors: 0 },
            shoppingList: { migrated: 0, errors: 0 },
          },
        };
      }
    }

    // Get data from localStorage
    const localData = getLocalStorageData();

    // Migrate in order: ingredients -> glass types -> cocktails -> favorites -> shopping list
    const ingredientsResult = await migrateIngredients(localData.ingredients);
    const glassTypesResult = await migrateGlassTypes(localData.glassTypes);
    const cocktailsResult = await migrateCocktails(localData.cocktails);
    const favoritesResult = await migrateFavorites(localData.favorites);
    const shoppingListResult = await migrateShoppingList(localData.shoppingList);

    const totalMigrated = 
      ingredientsResult.migrated + 
      glassTypesResult.migrated + 
      cocktailsResult.migrated + 
      favoritesResult.migrated + 
      shoppingListResult.migrated;

    const totalErrors = 
      ingredientsResult.errors + 
      glassTypesResult.errors + 
      cocktailsResult.errors + 
      favoritesResult.errors + 
      shoppingListResult.errors;

    const success = totalErrors === 0;
    const message = success 
      ? `Migration completed successfully! Migrated ${totalMigrated} items.`
      : `Migration completed with ${totalErrors} errors. Migrated ${totalMigrated} items.`;

    console.log(message);

    return {
      success,
      message,
      details: {
        ingredients: ingredientsResult,
        glassTypes: glassTypesResult,
        cocktails: cocktailsResult,
        favorites: favoritesResult,
        shoppingList: shoppingListResult,
      },
    };
  } catch (error) {
    console.error('Migration failed:', error);
    return {
      success: false,
      message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: {
        ingredients: { migrated: 0, errors: 0 },
        glassTypes: { migrated: 0, errors: 0 },
        cocktails: { migrated: 0, errors: 0 },
        favorites: { migrated: 0, errors: 0 },
        shoppingList: { migrated: 0, errors: 0 },
      },
    };
  }
}
