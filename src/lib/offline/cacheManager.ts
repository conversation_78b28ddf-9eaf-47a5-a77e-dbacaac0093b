'use client';

import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';

// Cache keys
const CACHE_KEYS = {
  COCKTAILS: 'cocktailflow-cache-cocktails',
  INGREDIENTS: 'cocktailflow-cache-ingredients',
  GLASS_TYPES: 'cocktailflow-cache-glass-types',
  FAVORITES: 'cocktailflow-cache-favorites',
  SHOPPING_LIST: 'cocktailflow-cache-shopping-list',
  LAST_SYNC: 'cocktailflow-cache-last-sync',
} as const;

// Cache expiration time (1 hour)
const CACHE_EXPIRATION = 60 * 60 * 1000;

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  version: string;
}

export interface CacheStatus {
  isOnline: boolean;
  lastSync: number | null;
  cacheSize: number;
  hasValidCache: boolean;
}

/**
 * Check if cache entry is valid (not expired)
 */
function isCacheValid<T>(entry: CacheEntry<T> | null): boolean {
  if (!entry) return false;
  return Date.now() - entry.timestamp < CACHE_EXPIRATION;
}

/**
 * Create a cache entry
 */
function createCacheEntry<T>(data: T): CacheEntry<T> {
  return {
    data,
    timestamp: Date.now(),
    version: '1.0.0',
  };
}

/**
 * Get data from cache
 */
function getFromCache<T>(key: string): T | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const cached = localStorage.getItem(key);
    if (!cached) return null;
    
    const entry: CacheEntry<T> = JSON.parse(cached);
    return isCacheValid(entry) ? entry.data : null;
  } catch (error) {
    console.error(`Error reading from cache (${key}):`, error);
    return null;
  }
}

/**
 * Set data in cache
 */
function setInCache<T>(key: string, data: T): void {
  if (typeof window === 'undefined') return;
  
  try {
    const entry = createCacheEntry(data);
    localStorage.setItem(key, JSON.stringify(entry));
  } catch (error) {
    console.error(`Error writing to cache (${key}):`, error);
  }
}

/**
 * Clear specific cache entry
 */
function clearCacheEntry(key: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error clearing cache entry (${key}):`, error);
  }
}

/**
 * Cache cocktails
 */
export function cacheCocktails(cocktails: Cocktail[]): void {
  setInCache(CACHE_KEYS.COCKTAILS, cocktails);
}

/**
 * Get cached cocktails
 */
export function getCachedCocktails(): Cocktail[] | null {
  return getFromCache<Cocktail[]>(CACHE_KEYS.COCKTAILS);
}

/**
 * Cache ingredients
 */
export function cacheIngredients(ingredients: Ingredient[]): void {
  setInCache(CACHE_KEYS.INGREDIENTS, ingredients);
}

/**
 * Get cached ingredients
 */
export function getCachedIngredients(): Ingredient[] | null {
  return getFromCache<Ingredient[]>(CACHE_KEYS.INGREDIENTS);
}

/**
 * Cache glass types
 */
export function cacheGlassTypes(glassTypes: GlassType[]): void {
  setInCache(CACHE_KEYS.GLASS_TYPES, glassTypes);
}

/**
 * Get cached glass types
 */
export function getCachedGlassTypes(): GlassType[] | null {
  return getFromCache<GlassType[]>(CACHE_KEYS.GLASS_TYPES);
}

/**
 * Cache favorites
 */
export function cacheFavorites(favorites: string[]): void {
  setInCache(CACHE_KEYS.FAVORITES, favorites);
}

/**
 * Get cached favorites
 */
export function getCachedFavorites(): string[] | null {
  return getFromCache<string[]>(CACHE_KEYS.FAVORITES);
}

/**
 * Cache shopping list
 */
export function cacheShoppingList(shoppingList: any[]): void {
  setInCache(CACHE_KEYS.SHOPPING_LIST, shoppingList);
}

/**
 * Get cached shopping list
 */
export function getCachedShoppingList(): any[] | null {
  return getFromCache<any[]>(CACHE_KEYS.SHOPPING_LIST);
}

/**
 * Update last sync timestamp
 */
export function updateLastSync(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(CACHE_KEYS.LAST_SYNC, Date.now().toString());
  } catch (error) {
    console.error('Error updating last sync:', error);
  }
}

/**
 * Get last sync timestamp
 */
export function getLastSync(): number | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const lastSync = localStorage.getItem(CACHE_KEYS.LAST_SYNC);
    return lastSync ? parseInt(lastSync, 10) : null;
  } catch (error) {
    console.error('Error getting last sync:', error);
    return null;
  }
}

/**
 * Clear all cache
 */
export function clearAllCache(): void {
  if (typeof window === 'undefined') return;
  
  Object.values(CACHE_KEYS).forEach(key => {
    clearCacheEntry(key);
  });
}

/**
 * Get cache status
 */
export function getCacheStatus(): CacheStatus {
  if (typeof window === 'undefined') {
    return {
      isOnline: false,
      lastSync: null,
      cacheSize: 0,
      hasValidCache: false,
    };
  }
  
  const isOnline = navigator.onLine;
  const lastSync = getLastSync();
  
  // Calculate cache size (approximate)
  let cacheSize = 0;
  Object.values(CACHE_KEYS).forEach(key => {
    try {
      const item = localStorage.getItem(key);
      if (item) {
        cacheSize += item.length;
      }
    } catch (error) {
      // Ignore errors
    }
  });
  
  // Check if we have valid cached data
  const hasValidCache = !!(
    getCachedCocktails() ||
    getCachedIngredients() ||
    getCachedGlassTypes()
  );
  
  return {
    isOnline,
    lastSync,
    cacheSize,
    hasValidCache,
  };
}

/**
 * Check if we're in offline mode
 */
export function isOfflineMode(): boolean {
  if (typeof window === 'undefined') return false;
  return !navigator.onLine;
}

/**
 * Setup offline event listeners
 */
export function setupOfflineListeners(): void {
  if (typeof window === 'undefined') return;
  
  const handleOnline = () => {
    console.log('App is back online');
    // Trigger data sync when back online
    window.dispatchEvent(new CustomEvent('cocktailflow:online'));
  };
  
  const handleOffline = () => {
    console.log('App is offline');
    window.dispatchEvent(new CustomEvent('cocktailflow:offline'));
  };
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // Cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
}
