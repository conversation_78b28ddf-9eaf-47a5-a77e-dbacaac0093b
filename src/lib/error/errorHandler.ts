'use client';

export enum ErrorType {
  NETWORK = 'network',
  DATABASE = 'database',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  UNKNOWN = 'unknown',
}

export interface AppError {
  type: ErrorType;
  message: string;
  originalError?: Error;
  timestamp: number;
  context?: Record<string, any>;
}

export interface ErrorHandlerOptions {
  showToUser?: boolean;
  logToConsole?: boolean;
  fallbackToCache?: boolean;
  retryable?: boolean;
}

/**
 * Create a standardized app error
 */
export function createAppError(
  type: ErrorType,
  message: string,
  originalError?: Error,
  context?: Record<string, any>
): AppError {
  return {
    type,
    message,
    originalError,
    timestamp: Date.now(),
    context,
  };
}

/**
 * Determine error type from error object
 */
export function determineErrorType(error: any): ErrorType {
  if (!error) return ErrorType.UNKNOWN;
  
  // Network errors
  if (error.name === 'NetworkError' || 
      error.message?.includes('fetch') ||
      error.message?.includes('network') ||
      error.code === 'NETWORK_ERROR') {
    return ErrorType.NETWORK;
  }
  
  // Supabase/Database errors
  if (error.code?.startsWith('PGRST') || 
      error.message?.includes('supabase') ||
      error.message?.includes('database')) {
    return ErrorType.DATABASE;
  }
  
  // Authentication errors
  if (error.code === 'UNAUTHORIZED' ||
      error.message?.includes('auth') ||
      error.message?.includes('permission')) {
    return ErrorType.AUTHENTICATION;
  }
  
  // Validation errors
  if (error.name === 'ValidationError' ||
      error.message?.includes('validation') ||
      error.message?.includes('invalid')) {
    return ErrorType.VALIDATION;
  }
  
  return ErrorType.UNKNOWN;
}

/**
 * Handle errors with appropriate fallback strategies
 */
export function handleError(
  error: any,
  context: string,
  options: ErrorHandlerOptions = {}
): AppError {
  const {
    showToUser = false,
    logToConsole = true,
    fallbackToCache = false,
    retryable = false,
  } = options;
  
  const errorType = determineErrorType(error);
  const appError = createAppError(
    errorType,
    error.message || 'An unknown error occurred',
    error,
    { context, retryable }
  );
  
  // Log to console if enabled
  if (logToConsole) {
    console.error(`[${context}] ${errorType.toUpperCase()}:`, {
      message: appError.message,
      originalError: error,
      context: appError.context,
    });
  }
  
  // Show to user if enabled
  if (showToUser) {
    showErrorToUser(appError);
  }
  
  // Trigger fallback to cache if enabled and it's a network error
  if (fallbackToCache && errorType === ErrorType.NETWORK) {
    console.log('Falling back to cached data due to network error');
    window.dispatchEvent(new CustomEvent('cocktailflow:fallback-to-cache', {
      detail: { error: appError, context }
    }));
  }
  
  return appError;
}

/**
 * Show error message to user (you can customize this based on your UI framework)
 */
function showErrorToUser(error: AppError): void {
  // For now, just log to console. In a real app, you'd show a toast/notification
  console.warn('User-facing error:', getUserFriendlyMessage(error));
  
  // Dispatch custom event that UI components can listen to
  window.dispatchEvent(new CustomEvent('cocktailflow:user-error', {
    detail: { error, message: getUserFriendlyMessage(error) }
  }));
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyMessage(error: AppError): string {
  switch (error.type) {
    case ErrorType.NETWORK:
      return 'Unable to connect to the server. Please check your internet connection.';
    case ErrorType.DATABASE:
      return 'There was a problem accessing the data. Please try again later.';
    case ErrorType.AUTHENTICATION:
      return 'You need to be logged in to perform this action.';
    case ErrorType.VALIDATION:
      return 'Please check your input and try again.';
    default:
      return 'Something went wrong. Please try again later.';
  }
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: AppError): boolean {
  return error.type === ErrorType.NETWORK || 
         (error.type === ErrorType.DATABASE && !error.message.includes('constraint'));
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      const appError = createAppError(
        determineErrorType(error),
        error.message || 'Retry attempt failed',
        error,
        { attempt, maxRetries }
      );
      
      if (!isRetryableError(appError)) {
        throw error;
      }
      
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Wrapper for async operations with error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: string,
  options: ErrorHandlerOptions = {}
): Promise<T | null> {
  try {
    return await operation();
  } catch (error) {
    handleError(error, context, options);
    return null;
  }
}

/**
 * Setup global error handlers
 */
export function setupGlobalErrorHandlers(): void {
  if (typeof window === 'undefined') return;
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = createAppError(
      ErrorType.UNKNOWN,
      'Unhandled promise rejection',
      event.reason,
      { type: 'unhandledrejection' }
    );
    
    console.error('Unhandled promise rejection:', error);
    event.preventDefault();
  });
  
  // Handle general errors
  window.addEventListener('error', (event) => {
    const error = createAppError(
      ErrorType.UNKNOWN,
      'Global error',
      event.error,
      { 
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      }
    );
    
    console.error('Global error:', error);
  });
}
