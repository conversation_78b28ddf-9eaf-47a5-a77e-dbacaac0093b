'use client';

import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { 
  getCocktails as getSupabaseCocktails,
  getIngredients as getSupabaseIngredients,
  getGlassTypes as getSupabaseGlassTypes,
  getFavoriteIds as getSupabaseFavoriteIds,
  getShopping<PERSON>ist as getSupabaseShoppingList,
} from '@/lib/supabase';
import {
  cacheCocktails,
  cacheIngredients,
  cacheGlassTypes,
  cacheFavorites,
  cacheShoppingList,
  getCachedCocktails,
  getCachedIngredients,
  getCachedGlassTypes,
  getCachedFavorites,
  getCachedShoppingList,
  updateLastSync,
  isOfflineMode,
} from '@/lib/offline/cacheManager';
import {
  handleError,
  withErrorHandling,
  retryWithBackoff,
  ErrorType,
} from '@/lib/error/errorHandler';

// Fallback to default data
import { cocktails as defaultCocktails } from '@/data/cocktails';
import { ingredients as defaultIngredients, glassTypes as defaultGlassTypes } from '@/data/ingredients';

/**
 * Resilient service for getting cocktails with offline support
 */
export async function getResilientCocktails(): Promise<Cocktail[]> {
  // If offline, return cached data immediately
  if (isOfflineMode()) {
    const cached = getCachedCocktails();
    if (cached) {
      console.log('Using cached cocktails (offline mode)');
      return cached;
    }
    console.log('No cached cocktails available, using default data');
    return defaultCocktails;
  }

  // Try to get from Supabase with retry
  const result = await withErrorHandling(
    () => retryWithBackoff(() => getSupabaseCocktails(), 2, 1000),
    'getResilientCocktails',
    { fallbackToCache: true }
  );

  if (result) {
    // Cache successful result
    cacheCocktails(result);
    updateLastSync();
    return result;
  }

  // Fallback to cached data
  const cached = getCachedCocktails();
  if (cached) {
    console.log('Using cached cocktails (Supabase failed)');
    return cached;
  }

  // Final fallback to default data
  console.log('Using default cocktails (no cache available)');
  return defaultCocktails;
}

/**
 * Resilient service for getting ingredients with offline support
 */
export async function getResilientIngredients(): Promise<Ingredient[]> {
  // If offline, return cached data immediately
  if (isOfflineMode()) {
    const cached = getCachedIngredients();
    if (cached) {
      console.log('Using cached ingredients (offline mode)');
      return cached;
    }
    console.log('No cached ingredients available, using default data');
    return defaultIngredients;
  }

  // Try to get from Supabase with retry
  const result = await withErrorHandling(
    () => retryWithBackoff(() => getSupabaseIngredients(), 2, 1000),
    'getResilientIngredients',
    { fallbackToCache: true }
  );

  if (result) {
    // Cache successful result
    cacheIngredients(result);
    updateLastSync();
    return result;
  }

  // Fallback to cached data
  const cached = getCachedIngredients();
  if (cached) {
    console.log('Using cached ingredients (Supabase failed)');
    return cached;
  }

  // Final fallback to default data
  console.log('Using default ingredients (no cache available)');
  return defaultIngredients;
}

/**
 * Resilient service for getting glass types with offline support
 */
export async function getResilientGlassTypes(): Promise<GlassType[]> {
  // If offline, return cached data immediately
  if (isOfflineMode()) {
    const cached = getCachedGlassTypes();
    if (cached) {
      console.log('Using cached glass types (offline mode)');
      return cached;
    }
    console.log('No cached glass types available, using default data');
    return defaultGlassTypes;
  }

  // Try to get from Supabase with retry
  const result = await withErrorHandling(
    () => retryWithBackoff(() => getSupabaseGlassTypes(), 2, 1000),
    'getResilientGlassTypes',
    { fallbackToCache: true }
  );

  if (result) {
    // Cache successful result
    cacheGlassTypes(result);
    updateLastSync();
    return result;
  }

  // Fallback to cached data
  const cached = getCachedGlassTypes();
  if (cached) {
    console.log('Using cached glass types (Supabase failed)');
    return cached;
  }

  // Final fallback to default data
  console.log('Using default glass types (no cache available)');
  return defaultGlassTypes;
}

/**
 * Resilient service for getting favorites with offline support
 */
export async function getResilientFavorites(): Promise<string[]> {
  // If offline, return cached data immediately
  if (isOfflineMode()) {
    const cached = getCachedFavorites();
    if (cached) {
      console.log('Using cached favorites (offline mode)');
      return cached;
    }
    console.log('No cached favorites available');
    return [];
  }

  // Try to get from Supabase with retry
  const result = await withErrorHandling(
    () => retryWithBackoff(() => getSupabaseFavoriteIds(), 2, 1000),
    'getResilientFavorites',
    { fallbackToCache: true }
  );

  if (result) {
    // Cache successful result
    cacheFavorites(result);
    updateLastSync();
    return result;
  }

  // Fallback to cached data
  const cached = getCachedFavorites();
  if (cached) {
    console.log('Using cached favorites (Supabase failed)');
    return cached;
  }

  // No fallback for favorites
  return [];
}

/**
 * Resilient service for getting shopping list with offline support
 */
export async function getResilientShoppingList(): Promise<any[]> {
  // If offline, return cached data immediately
  if (isOfflineMode()) {
    const cached = getCachedShoppingList();
    if (cached) {
      console.log('Using cached shopping list (offline mode)');
      return cached;
    }
    console.log('No cached shopping list available');
    return [];
  }

  // Try to get from Supabase with retry
  const result = await withErrorHandling(
    () => retryWithBackoff(() => getSupabaseShoppingList(), 2, 1000),
    'getResilientShoppingList',
    { fallbackToCache: true }
  );

  if (result) {
    // Cache successful result
    cacheShoppingList(result);
    updateLastSync();
    return result;
  }

  // Fallback to cached data
  const cached = getCachedShoppingList();
  if (cached) {
    console.log('Using cached shopping list (Supabase failed)');
    return cached;
  }

  // No fallback for shopping list
  return [];
}

/**
 * Sync all data when coming back online
 */
export async function syncAllData(): Promise<void> {
  console.log('Syncing all data...');
  
  try {
    const [cocktails, ingredients, glassTypes, favorites, shoppingList] = await Promise.allSettled([
      getResilientCocktails(),
      getResilientIngredients(),
      getResilientGlassTypes(),
      getResilientFavorites(),
      getResilientShoppingList(),
    ]);

    console.log('Data sync completed:', {
      cocktails: cocktails.status,
      ingredients: ingredients.status,
      glassTypes: glassTypes.status,
      favorites: favorites.status,
      shoppingList: shoppingList.status,
    });
  } catch (error) {
    console.error('Error during data sync:', error);
  }
}

/**
 * Initialize resilient data service
 */
export function initializeResilientDataService(): void {
  if (typeof window === 'undefined') return;

  // Setup offline/online event listeners
  const handleOnline = () => {
    console.log('Back online - syncing data');
    syncAllData();
  };

  const handleOffline = () => {
    console.log('Gone offline - will use cached data');
  };

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  // Cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
}
