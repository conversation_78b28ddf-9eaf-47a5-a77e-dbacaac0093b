import { createClient } from '@supabase/supabase-js';

// Function to get environment variables with fallback
function getEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`Missing environment variable: ${name}`);
  }
  return value;
}

// Create supabase client lazily
let _supabase: ReturnType<typeof createClient> | null = null;

export const supabase = new Proxy({} as ReturnType<typeof createClient>, {
  get(target, prop) {
    if (!_supabase) {
      const supabaseUrl = getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
      const supabaseAnonKey = getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY');

      _supabase = createClient(supabaseUrl, supabaseAnonKey, {
        auth: {
          persistSession: true,
          autoRefreshToken: true,
        },
      });
    }
    return _supabase[prop as keyof typeof _supabase];
  }
});

// Database table names
export const TABLES = {
  COCKTAILS: 'cocktails',
  INGREDIENTS: 'ingredients',
  GLASS_TYPES: 'glass_types',
  FAVORITES: 'favorites',
  SHOPPING_LIST: 'shopping_list',
  ADMIN_SESSIONS: 'admin_sessions',
  COCKTAIL_INGREDIENTS: 'cocktail_ingredients',
} as const;

// Type definitions for database tables
export interface DatabaseCocktail {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  glass_type_id: string;
  category: string;
  tags: string[];
  difficulty: string;
  prep_time: number;
  servings: number;
  image_url?: string;
  garnish?: string;
  history?: string;
  variations?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface DatabaseIngredient {
  id: string;
  name: string;
  category: string;
  alcoholic: boolean;
  description?: string;
  abv?: number;
  created_at?: string;
  updated_at?: string;
}

export interface DatabaseGlassType {
  id: string;
  name: string;
  description: string;
  icon_url?: string;
  capacity?: string;
  created_at?: string;
  updated_at?: string;
}

export interface DatabaseCocktailIngredient {
  id: string;
  cocktail_id: string;
  ingredient_id: string;
  amount: string;
  optional: boolean;
  garnish: boolean;
  created_at?: string;
}

export interface DatabaseFavorite {
  id: string;
  cocktail_id: string;
  user_session: string; // For now, we'll use a session identifier
  created_at?: string;
}

export interface DatabaseShoppingListItem {
  id: string;
  ingredient_id: string;
  amount: string;
  cocktail_names: string[];
  user_session: string; // For now, we'll use a session identifier
  created_at?: string;
  updated_at?: string;
}

export interface DatabaseAdminSession {
  id: string;
  session_token: string;
  is_authenticated: boolean;
  login_time: string;
  expires_at: string;
  created_at?: string;
  updated_at?: string;
}
