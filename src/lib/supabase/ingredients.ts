import { supabase, TABLES, DatabaseIngredient } from '../supabase';
import { Ingredient, IngredientCategory } from '@/types/cocktail';

/**
 * Convert database ingredient to application ingredient
 */
function dbToIngredient(dbIngredient: DatabaseIngredient): Ingredient {
  return {
    id: dbIngredient.id,
    name: dbIngredient.name,
    category: dbIngredient.category as IngredientCategory,
    alcoholic: dbIngredient.alcoholic,
    description: dbIngredient.description,
    abv: dbIngredient.abv,
  };
}

/**
 * Convert application ingredient to database ingredient
 */
function ingredientToDb(ingredient: Ingredient): Omit<DatabaseIngredient, 'created_at' | 'updated_at'> {
  return {
    id: ingredient.id,
    name: ingredient.name,
    category: ingredient.category,
    alcoholic: ingredient.alcoholic,
    description: ingredient.description,
    abv: ingredient.abv,
  };
}

/**
 * Get all ingredients from Supabase
 */
export async function getIngredients(): Promise<Ingredient[]> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching ingredients:', error);
      throw error;
    }

    return data?.map(dbToIngredient) || [];
  } catch (error) {
    console.error('Error in getIngredients:', error);
    throw error;
  }
}

/**
 * Get ingredient by ID
 */
export async function getIngredientById(id: string): Promise<Ingredient | null> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching ingredient:', error);
      throw error;
    }

    return data ? dbToIngredient(data) : null;
  } catch (error) {
    console.error('Error in getIngredientById:', error);
    throw error;
  }
}

/**
 * Create a new ingredient
 */
export async function createIngredient(ingredient: Ingredient): Promise<Ingredient> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .insert(ingredientToDb(ingredient))
      .select()
      .single();

    if (error) {
      console.error('Error creating ingredient:', error);
      throw error;
    }

    return dbToIngredient(data);
  } catch (error) {
    console.error('Error in createIngredient:', error);
    throw error;
  }
}

/**
 * Update an existing ingredient
 */
export async function updateIngredient(id: string, updates: Partial<Ingredient>): Promise<Ingredient> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating ingredient:', error);
      throw error;
    }

    return dbToIngredient(data);
  } catch (error) {
    console.error('Error in updateIngredient:', error);
    throw error;
  }
}

/**
 * Delete an ingredient
 */
export async function deleteIngredient(id: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from(TABLES.INGREDIENTS)
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting ingredient:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteIngredient:', error);
    throw error;
  }
}

/**
 * Get ingredients by category
 */
export async function getIngredientsByCategory(category: IngredientCategory): Promise<Ingredient[]> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .select('*')
      .eq('category', category)
      .order('name');

    if (error) {
      console.error('Error fetching ingredients by category:', error);
      throw error;
    }

    return data?.map(dbToIngredient) || [];
  } catch (error) {
    console.error('Error in getIngredientsByCategory:', error);
    throw error;
  }
}

/**
 * Search ingredients by name
 */
export async function searchIngredients(query: string): Promise<Ingredient[]> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .select('*')
      .ilike('name', `%${query}%`)
      .order('name');

    if (error) {
      console.error('Error searching ingredients:', error);
      throw error;
    }

    return data?.map(dbToIngredient) || [];
  } catch (error) {
    console.error('Error in searchIngredients:', error);
    throw error;
  }
}
