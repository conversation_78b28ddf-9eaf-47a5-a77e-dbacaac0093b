import { supabase, TABLES, DatabaseShoppingListItem } from '../supabase';
import { Ingredient } from '@/types/cocktail';
import { getIngredientById } from './ingredients';

export interface ShoppingListItem {
  ingredient: Ingredient;
  amount: string;
  cocktails: string[]; // cocktail names that use this ingredient
}

/**
 * Get user session identifier
 */
function getUserSession(): string {
  if (typeof window === 'undefined') return 'server-session';
  
  let sessionId = sessionStorage.getItem('cocktailflow-session');
  if (!sessionId) {
    sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('cocktailflow-session', sessionId);
  }
  return sessionId;
}

/**
 * Convert database shopping list item to application format
 */
async function dbToShoppingListItem(dbItem: DatabaseShoppingListItem): Promise<ShoppingListItem | null> {
  const ingredient = await getIngredientById(dbItem.ingredient_id);
  if (!ingredient) {
    return null;
  }

  return {
    ingredient,
    amount: dbItem.amount,
    cocktails: dbItem.cocktail_names,
  };
}

/**
 * Get shopping list for the current user session
 */
export async function getShoppingList(): Promise<ShoppingListItem[]> {
  try {
    const userSession = getUserSession();
    
    const { data, error } = await supabase
      .from(TABLES.SHOPPING_LIST)
      .select('*')
      .eq('user_session', userSession)
      .order('created_at');

    if (error) {
      console.error('Error fetching shopping list:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Convert to application format
    const items: ShoppingListItem[] = [];
    for (const dbItem of data) {
      const item = await dbToShoppingListItem(dbItem);
      if (item) {
        items.push(item);
      }
    }

    return items;
  } catch (error) {
    console.error('Error in getShoppingList:', error);
    return [];
  }
}

/**
 * Add an item to the shopping list
 */
export async function addToShoppingList(
  ingredientId: string,
  amount: string,
  cocktailNames: string[] = []
): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    // Check if item already exists
    const { data: existingItem, error: checkError } = await supabase
      .from(TABLES.SHOPPING_LIST)
      .select('*')
      .eq('ingredient_id', ingredientId)
      .eq('user_session', userSession)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing shopping list item:', checkError);
      throw checkError;
    }

    if (existingItem) {
      // Update existing item - merge cocktail names
      const mergedCocktails = [...new Set([...existingItem.cocktail_names, ...cocktailNames])];
      
      const { error: updateError } = await supabase
        .from(TABLES.SHOPPING_LIST)
        .update({
          amount,
          cocktail_names: mergedCocktails,
        })
        .eq('id', existingItem.id);

      if (updateError) {
        console.error('Error updating shopping list item:', updateError);
        throw updateError;
      }
    } else {
      // Create new item
      const { error: insertError } = await supabase
        .from(TABLES.SHOPPING_LIST)
        .insert({
          ingredient_id: ingredientId,
          amount,
          cocktail_names: cocktailNames,
          user_session: userSession,
        });

      if (insertError) {
        console.error('Error adding to shopping list:', insertError);
        throw insertError;
      }
    }

    return true;
  } catch (error) {
    console.error('Error in addToShoppingList:', error);
    return false;
  }
}

/**
 * Remove an item from the shopping list
 */
export async function removeFromShoppingList(ingredientId: string): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { error } = await supabase
      .from(TABLES.SHOPPING_LIST)
      .delete()
      .eq('ingredient_id', ingredientId)
      .eq('user_session', userSession);

    if (error) {
      console.error('Error removing from shopping list:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in removeFromShoppingList:', error);
    return false;
  }
}

/**
 * Update shopping list item amount
 */
export async function updateShoppingListAmount(
  ingredientId: string,
  amount: string
): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { error } = await supabase
      .from(TABLES.SHOPPING_LIST)
      .update({ amount })
      .eq('ingredient_id', ingredientId)
      .eq('user_session', userSession);

    if (error) {
      console.error('Error updating shopping list amount:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in updateShoppingListAmount:', error);
    return false;
  }
}

/**
 * Clear the entire shopping list for the current user session
 */
export async function clearShoppingList(): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { error } = await supabase
      .from(TABLES.SHOPPING_LIST)
      .delete()
      .eq('user_session', userSession);

    if (error) {
      console.error('Error clearing shopping list:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in clearShoppingList:', error);
    return false;
  }
}

/**
 * Check if an ingredient is in the shopping list
 */
export async function isInShoppingList(ingredientId: string): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { data, error } = await supabase
      .from(TABLES.SHOPPING_LIST)
      .select('id')
      .eq('ingredient_id', ingredientId)
      .eq('user_session', userSession)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return false; // Not found
      }
      console.error('Error checking shopping list:', error);
      throw error;
    }

    return !!data;
  } catch (error) {
    console.error('Error in isInShoppingList:', error);
    return false;
  }
}

/**
 * Get shopping list count
 */
export async function getShoppingListCount(): Promise<number> {
  try {
    const userSession = getUserSession();
    
    const { count, error } = await supabase
      .from(TABLES.SHOPPING_LIST)
      .select('*', { count: 'exact', head: true })
      .eq('user_session', userSession);

    if (error) {
      console.error('Error getting shopping list count:', error);
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getShoppingListCount:', error);
    return 0;
  }
}
