import { supabase, TABLES, DatabaseGlassType } from '../supabase';
import { GlassType } from '@/types/cocktail';

/**
 * Convert database glass type to application glass type
 */
function dbToGlassType(dbGlassType: DatabaseGlassType): GlassType {
  return {
    id: dbGlassType.id,
    name: dbGlassType.name,
    description: dbGlassType.description,
    iconUrl: dbGlassType.icon_url,
    capacity: dbGlassType.capacity,
  };
}

/**
 * Convert application glass type to database glass type
 */
function glassTypeToDb(glassType: GlassType): Omit<DatabaseGlassType, 'created_at' | 'updated_at'> {
  return {
    id: glassType.id,
    name: glassType.name,
    description: glassType.description,
    icon_url: glassType.iconUrl,
    capacity: glassType.capacity,
  };
}

/**
 * Get all glass types from Supabase
 */
export async function getGlassTypes(): Promise<GlassType[]> {
  try {
    const { data, error } = await supabase
      .from(TABLES.GLASS_TYPES)
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching glass types:', error);
      throw error;
    }

    return data?.map(dbToGlassType) || [];
  } catch (error) {
    console.error('Error in getGlassTypes:', error);
    throw error;
  }
}

/**
 * Get glass type by ID
 */
export async function getGlassTypeById(id: string): Promise<GlassType | null> {
  try {
    const { data, error } = await supabase
      .from(TABLES.GLASS_TYPES)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching glass type:', error);
      throw error;
    }

    return data ? dbToGlassType(data) : null;
  } catch (error) {
    console.error('Error in getGlassTypeById:', error);
    throw error;
  }
}

/**
 * Create a new glass type
 */
export async function createGlassType(glassType: GlassType): Promise<GlassType> {
  try {
    const { data, error } = await supabase
      .from(TABLES.GLASS_TYPES)
      .insert(glassTypeToDb(glassType))
      .select()
      .single();

    if (error) {
      console.error('Error creating glass type:', error);
      throw error;
    }

    return dbToGlassType(data);
  } catch (error) {
    console.error('Error in createGlassType:', error);
    throw error;
  }
}

/**
 * Update an existing glass type
 */
export async function updateGlassType(id: string, updates: Partial<GlassType>): Promise<GlassType> {
  try {
    const dbUpdates: Partial<DatabaseGlassType> = {};
    
    if (updates.name !== undefined) dbUpdates.name = updates.name;
    if (updates.description !== undefined) dbUpdates.description = updates.description;
    if (updates.iconUrl !== undefined) dbUpdates.icon_url = updates.iconUrl;
    if (updates.capacity !== undefined) dbUpdates.capacity = updates.capacity;

    const { data, error } = await supabase
      .from(TABLES.GLASS_TYPES)
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating glass type:', error);
      throw error;
    }

    return dbToGlassType(data);
  } catch (error) {
    console.error('Error in updateGlassType:', error);
    throw error;
  }
}

/**
 * Delete a glass type
 */
export async function deleteGlassType(id: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from(TABLES.GLASS_TYPES)
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting glass type:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteGlassType:', error);
    throw error;
  }
}

/**
 * Search glass types by name
 */
export async function searchGlassTypes(query: string): Promise<GlassType[]> {
  try {
    const { data, error } = await supabase
      .from(TABLES.GLASS_TYPES)
      .select('*')
      .ilike('name', `%${query}%`)
      .order('name');

    if (error) {
      console.error('Error searching glass types:', error);
      throw error;
    }

    return data?.map(dbToGlassType) || [];
  } catch (error) {
    console.error('Error in searchGlassTypes:', error);
    throw error;
  }
}
