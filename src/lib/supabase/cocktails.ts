import { supabase, TABLES, DatabaseCocktail, DatabaseCocktailIngredient } from '../supabase';
import { Cocktail, CocktailIngredient, CocktailCategory, Difficulty } from '@/types/cocktail';
import { getIngredientById } from './ingredients';
import { getGlassTypeById } from './glassTypes';

/**
 * Convert database cocktail to application cocktail
 */
async function dbToCocktail(dbCocktail: DatabaseCocktail, dbIngredients?: DatabaseCocktailIngredient[]): Promise<Cocktail> {
  // Get glass type
  const glassType = await getGlassTypeById(dbCocktail.glass_type_id);
  if (!glassType) {
    throw new Error(`Glass type not found: ${dbCocktail.glass_type_id}`);
  }

  // Get cocktail ingredients if not provided
  let cocktailIngredients = dbIngredients;
  if (!cocktailIngredients) {
    const { data, error } = await supabase
      .from(TABLES.COCKTAIL_INGREDIENTS)
      .select('*')
      .eq('cocktail_id', dbCocktail.id);
    
    if (error) {
      throw error;
    }
    cocktailIngredients = data || [];
  }

  // Convert ingredients
  const ingredients: CocktailIngredient[] = [];
  for (const dbIngredient of cocktailIngredients) {
    const ingredient = await getIngredientById(dbIngredient.ingredient_id);
    if (ingredient) {
      ingredients.push({
        ingredient,
        amount: dbIngredient.amount,
        optional: dbIngredient.optional,
        garnish: dbIngredient.garnish,
      });
    }
  }

  return {
    id: dbCocktail.id,
    name: dbCocktail.name,
    description: dbCocktail.description,
    instructions: dbCocktail.instructions,
    ingredients,
    glassType,
    category: dbCocktail.category as CocktailCategory,
    tags: dbCocktail.tags,
    difficulty: dbCocktail.difficulty as Difficulty,
    prepTime: dbCocktail.prep_time,
    servings: dbCocktail.servings,
    imageUrl: dbCocktail.image_url,
    garnish: dbCocktail.garnish,
    history: dbCocktail.history,
    variations: dbCocktail.variations,
  };
}

/**
 * Convert application cocktail to database cocktail
 */
function cocktailToDb(cocktail: Cocktail): Omit<DatabaseCocktail, 'created_at' | 'updated_at'> {
  return {
    id: cocktail.id,
    name: cocktail.name,
    description: cocktail.description,
    instructions: cocktail.instructions,
    glass_type_id: cocktail.glassType.id,
    category: cocktail.category,
    tags: cocktail.tags,
    difficulty: cocktail.difficulty,
    prep_time: cocktail.prepTime,
    servings: cocktail.servings,
    image_url: cocktail.imageUrl,
    garnish: cocktail.garnish,
    history: cocktail.history,
    variations: cocktail.variations,
  };
}

/**
 * Get all cocktails from Supabase
 */
export async function getCocktails(): Promise<Cocktail[]> {
  try {
    const { data: cocktailsData, error: cocktailsError } = await supabase
      .from(TABLES.COCKTAILS)
      .select('*')
      .order('name');

    if (cocktailsError) {
      console.error('Error fetching cocktails:', cocktailsError);
      throw cocktailsError;
    }

    if (!cocktailsData || cocktailsData.length === 0) {
      return [];
    }

    // Get all cocktail ingredients in one query
    const { data: ingredientsData, error: ingredientsError } = await supabase
      .from(TABLES.COCKTAIL_INGREDIENTS)
      .select('*')
      .in('cocktail_id', cocktailsData.map(c => c.id));

    if (ingredientsError) {
      console.error('Error fetching cocktail ingredients:', ingredientsError);
      throw ingredientsError;
    }

    // Group ingredients by cocktail ID
    const ingredientsBycocktail = new Map<string, DatabaseCocktailIngredient[]>();
    (ingredientsData || []).forEach(ingredient => {
      const cocktailId = ingredient.cocktail_id;
      if (!ingredientsBycocktail.has(cocktailId)) {
        ingredientsBycocktail.set(cocktailId, []);
      }
      ingredientsBycocktail.get(cocktailId)!.push(ingredient);
    });

    // Convert to application format
    const cocktails: Cocktail[] = [];
    for (const dbCocktail of cocktailsData) {
      const cocktailIngredients = ingredientsBycocktail.get(dbCocktail.id) || [];
      const cocktail = await dbToCocktail(dbCocktail, cocktailIngredients);
      cocktails.push(cocktail);
    }

    return cocktails;
  } catch (error) {
    console.error('Error in getCocktails:', error);
    throw error;
  }
}

/**
 * Get cocktail by ID
 */
export async function getCocktailById(id: string): Promise<Cocktail | null> {
  try {
    const { data, error } = await supabase
      .from(TABLES.COCKTAILS)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error fetching cocktail:', error);
      throw error;
    }

    return data ? await dbToCocktail(data) : null;
  } catch (error) {
    console.error('Error in getCocktailById:', error);
    throw error;
  }
}

/**
 * Create a new cocktail
 */
export async function createCocktail(cocktail: Cocktail): Promise<Cocktail> {
  try {
    // Start a transaction by creating the cocktail first
    const { data: cocktailData, error: cocktailError } = await supabase
      .from(TABLES.COCKTAILS)
      .insert(cocktailToDb(cocktail))
      .select()
      .single();

    if (cocktailError) {
      console.error('Error creating cocktail:', cocktailError);
      throw cocktailError;
    }

    // Create cocktail ingredients
    const ingredientInserts = cocktail.ingredients.map(ingredient => ({
      cocktail_id: cocktail.id,
      ingredient_id: ingredient.ingredient.id,
      amount: ingredient.amount,
      optional: ingredient.optional || false,
      garnish: ingredient.garnish || false,
    }));

    if (ingredientInserts.length > 0) {
      const { error: ingredientsError } = await supabase
        .from(TABLES.COCKTAIL_INGREDIENTS)
        .insert(ingredientInserts);

      if (ingredientsError) {
        // Rollback: delete the cocktail
        await supabase.from(TABLES.COCKTAILS).delete().eq('id', cocktail.id);
        console.error('Error creating cocktail ingredients:', ingredientsError);
        throw ingredientsError;
      }
    }

    return await dbToCocktail(cocktailData);
  } catch (error) {
    console.error('Error in createCocktail:', error);
    throw error;
  }
}

/**
 * Update an existing cocktail
 */
export async function updateCocktail(id: string, updates: Partial<Cocktail>): Promise<Cocktail> {
  try {
    // Update cocktail basic info
    const dbUpdates: Partial<DatabaseCocktail> = {};

    if (updates.name !== undefined) dbUpdates.name = updates.name;
    if (updates.description !== undefined) dbUpdates.description = updates.description;
    if (updates.instructions !== undefined) dbUpdates.instructions = updates.instructions;
    if (updates.glassType !== undefined) dbUpdates.glass_type_id = updates.glassType.id;
    if (updates.category !== undefined) dbUpdates.category = updates.category;
    if (updates.tags !== undefined) dbUpdates.tags = updates.tags;
    if (updates.difficulty !== undefined) dbUpdates.difficulty = updates.difficulty;
    if (updates.prepTime !== undefined) dbUpdates.prep_time = updates.prepTime;
    if (updates.servings !== undefined) dbUpdates.servings = updates.servings;
    if (updates.imageUrl !== undefined) dbUpdates.image_url = updates.imageUrl;
    if (updates.garnish !== undefined) dbUpdates.garnish = updates.garnish;
    if (updates.history !== undefined) dbUpdates.history = updates.history;
    if (updates.variations !== undefined) dbUpdates.variations = updates.variations;

    const { data: cocktailData, error: cocktailError } = await supabase
      .from(TABLES.COCKTAILS)
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();

    if (cocktailError) {
      console.error('Error updating cocktail:', cocktailError);
      throw cocktailError;
    }

    // Update ingredients if provided
    if (updates.ingredients !== undefined) {
      // Delete existing ingredients
      const { error: deleteError } = await supabase
        .from(TABLES.COCKTAIL_INGREDIENTS)
        .delete()
        .eq('cocktail_id', id);

      if (deleteError) {
        console.error('Error deleting cocktail ingredients:', deleteError);
        throw deleteError;
      }

      // Insert new ingredients
      if (updates.ingredients.length > 0) {
        const ingredientInserts = updates.ingredients.map(ingredient => ({
          cocktail_id: id,
          ingredient_id: ingredient.ingredient.id,
          amount: ingredient.amount,
          optional: ingredient.optional || false,
          garnish: ingredient.garnish || false,
        }));

        const { error: ingredientsError } = await supabase
          .from(TABLES.COCKTAIL_INGREDIENTS)
          .insert(ingredientInserts);

        if (ingredientsError) {
          console.error('Error creating cocktail ingredients:', ingredientsError);
          throw ingredientsError;
        }
      }
    }

    return await dbToCocktail(cocktailData);
  } catch (error) {
    console.error('Error in updateCocktail:', error);
    throw error;
  }
}

/**
 * Delete a cocktail
 */
export async function deleteCocktail(id: string): Promise<boolean> {
  try {
    // Delete cocktail ingredients first (cascade should handle this, but being explicit)
    const { error: ingredientsError } = await supabase
      .from(TABLES.COCKTAIL_INGREDIENTS)
      .delete()
      .eq('cocktail_id', id);

    if (ingredientsError) {
      console.error('Error deleting cocktail ingredients:', ingredientsError);
      throw ingredientsError;
    }

    // Delete the cocktail
    const { error: cocktailError } = await supabase
      .from(TABLES.COCKTAILS)
      .delete()
      .eq('id', id);

    if (cocktailError) {
      console.error('Error deleting cocktail:', cocktailError);
      throw cocktailError;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteCocktail:', error);
    throw error;
  }
}

/**
 * Search cocktails by name, description, or tags
 */
export async function searchCocktails(query: string): Promise<Cocktail[]> {
  try {
    const { data: cocktailsData, error: cocktailsError } = await supabase
      .from(TABLES.COCKTAILS)
      .select('*')
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,tags.cs.{${query}}`)
      .order('name');

    if (cocktailsError) {
      console.error('Error searching cocktails:', cocktailsError);
      throw cocktailsError;
    }

    if (!cocktailsData || cocktailsData.length === 0) {
      return [];
    }

    // Convert to application format
    const cocktails: Cocktail[] = [];
    for (const dbCocktail of cocktailsData) {
      const cocktail = await dbToCocktail(dbCocktail);
      cocktails.push(cocktail);
    }

    return cocktails;
  } catch (error) {
    console.error('Error in searchCocktails:', error);
    throw error;
  }
}

/**
 * Get cocktails by category
 */
export async function getCocktailsByCategory(category: CocktailCategory): Promise<Cocktail[]> {
  try {
    const { data: cocktailsData, error: cocktailsError } = await supabase
      .from(TABLES.COCKTAILS)
      .select('*')
      .eq('category', category)
      .order('name');

    if (cocktailsError) {
      console.error('Error fetching cocktails by category:', cocktailsError);
      throw cocktailsError;
    }

    if (!cocktailsData || cocktailsData.length === 0) {
      return [];
    }

    // Convert to application format
    const cocktails: Cocktail[] = [];
    for (const dbCocktail of cocktailsData) {
      const cocktail = await dbToCocktail(dbCocktail);
      cocktails.push(cocktail);
    }

    return cocktails;
  } catch (error) {
    console.error('Error in getCocktailsByCategory:', error);
    throw error;
  }
}

/**
 * Get cocktails by tag
 */
export async function getCocktailsByTag(tag: string): Promise<Cocktail[]> {
  try {
    const { data: cocktailsData, error: cocktailsError } = await supabase
      .from(TABLES.COCKTAILS)
      .select('*')
      .contains('tags', [tag])
      .order('name');

    if (cocktailsError) {
      console.error('Error fetching cocktails by tag:', cocktailsError);
      throw cocktailsError;
    }

    if (!cocktailsData || cocktailsData.length === 0) {
      return [];
    }

    // Convert to application format
    const cocktails: Cocktail[] = [];
    for (const dbCocktail of cocktailsData) {
      const cocktail = await dbToCocktail(dbCocktail);
      cocktails.push(cocktail);
    }

    return cocktails;
  } catch (error) {
    console.error('Error in getCocktailsByTag:', error);
    throw error;
  }
}
