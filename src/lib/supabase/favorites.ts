import { supabase, TABLES, DatabaseFavorite } from '../supabase';

/**
 * Get user session identifier
 * For now, we'll use a simple session ID stored in sessionStorage
 * In a real app, this would be tied to user authentication
 */
function getUserSession(): string {
  if (typeof window === 'undefined') return 'server-session';
  
  let sessionId = sessionStorage.getItem('cocktailflow-session');
  if (!sessionId) {
    sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('cocktailflow-session', sessionId);
  }
  return sessionId;
}

/**
 * Get all favorite cocktail IDs for the current user session
 */
export async function getFavoriteIds(): Promise<string[]> {
  try {
    const userSession = getUserSession();
    
    const { data, error } = await supabase
      .from(TABLES.FAVORITES)
      .select('cocktail_id')
      .eq('user_session', userSession);

    if (error) {
      console.error('Error fetching favorites:', error);
      throw error;
    }

    return data?.map(fav => fav.cocktail_id) || [];
  } catch (error) {
    console.error('Error in getFavoriteIds:', error);
    // Return empty array on error to maintain app functionality
    return [];
  }
}

/**
 * Check if a cocktail is favorited by the current user
 */
export async function isFavorite(cocktailId: string): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { data, error } = await supabase
      .from(TABLES.FAVORITES)
      .select('id')
      .eq('cocktail_id', cocktailId)
      .eq('user_session', userSession)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return false; // Not found
      }
      console.error('Error checking favorite:', error);
      throw error;
    }

    return !!data;
  } catch (error) {
    console.error('Error in isFavorite:', error);
    return false;
  }
}

/**
 * Add a cocktail to favorites
 */
export async function addFavorite(cocktailId: string): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { error } = await supabase
      .from(TABLES.FAVORITES)
      .insert({
        cocktail_id: cocktailId,
        user_session: userSession,
      });

    if (error) {
      // Handle duplicate key error (already favorited)
      if (error.code === '23505') {
        return true; // Already favorited, consider it success
      }
      console.error('Error adding favorite:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in addFavorite:', error);
    return false;
  }
}

/**
 * Remove a cocktail from favorites
 */
export async function removeFavorite(cocktailId: string): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { error } = await supabase
      .from(TABLES.FAVORITES)
      .delete()
      .eq('cocktail_id', cocktailId)
      .eq('user_session', userSession);

    if (error) {
      console.error('Error removing favorite:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in removeFavorite:', error);
    return false;
  }
}

/**
 * Toggle favorite status for a cocktail
 */
export async function toggleFavorite(cocktailId: string): Promise<boolean> {
  try {
    const isCurrentlyFavorite = await isFavorite(cocktailId);
    
    if (isCurrentlyFavorite) {
      await removeFavorite(cocktailId);
      return false;
    } else {
      await addFavorite(cocktailId);
      return true;
    }
  } catch (error) {
    console.error('Error in toggleFavorite:', error);
    return false;
  }
}

/**
 * Clear all favorites for the current user session
 */
export async function clearFavorites(): Promise<boolean> {
  try {
    const userSession = getUserSession();
    
    const { error } = await supabase
      .from(TABLES.FAVORITES)
      .delete()
      .eq('user_session', userSession);

    if (error) {
      console.error('Error clearing favorites:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in clearFavorites:', error);
    return false;
  }
}

/**
 * Get count of favorites for the current user session
 */
export async function getFavoritesCount(): Promise<number> {
  try {
    const userSession = getUserSession();
    
    const { count, error } = await supabase
      .from(TABLES.FAVORITES)
      .select('*', { count: 'exact', head: true })
      .eq('user_session', userSession);

    if (error) {
      console.error('Error getting favorites count:', error);
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getFavoritesCount:', error);
    return 0;
  }
}
