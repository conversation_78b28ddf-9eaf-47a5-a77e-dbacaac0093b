# CocktailFlow: LocalStorage to Supabase Migration Guide

## Overview

This guide walks you through migrating the CocktailFlow application from LocalStorage to Supabase for better data persistence, scalability, and reliability.

## 🚀 Quick Start

### 1. Database Setup

#### Option A: Manual Setup (Recommended)
1. Go to your Supabase project dashboard: https://uqbydhyrplrdhaqnkazc.supabase.co
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase/migrations/001_initial_schema.sql`
4. Execute the SQL to create all tables and relationships

#### Option B: Using the Setup Script
```bash
npx ts-node src/scripts/setupDatabase.ts
```

### 2. Environment Configuration

Ensure your `.env.local` file contains:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://uqbydhyrplrdhaqnkazc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVxYnlkaHlycGxyZGhhcW5rYXpjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MTQyMjYsImV4cCI6MjA2NzI5MDIyNn0.GYf2mqHxAFP8jIKAqUOF2jg4_tmqmj7oDqtAiaGGiV4

# Data Source Configuration
# Set to 'true' to use Supabase, 'false' to use LocalStorage
NEXT_PUBLIC_USE_SUPABASE=false
```

### 3. Data Migration

Run the migration script to transfer existing LocalStorage data:

```bash
npx ts-node src/scripts/migrateData.ts
```

Use `--force` flag to override existing data:

```bash
npx ts-node src/scripts/migrateData.ts --force
```

### 4. Enable Supabase

Update your `.env.local` file:

```env
NEXT_PUBLIC_USE_SUPABASE=true
```

### 5. Test Migration

Run the test suite to verify everything works:

```bash
npx ts-node src/scripts/testMigration.ts
```

## 📊 Database Schema

### Core Tables
- **ingredients**: Cocktail ingredients with categories and properties
- **glass_types**: Different types of glasses used for cocktails
- **cocktails**: Main cocktail recipes with metadata
- **cocktail_ingredients**: Junction table linking cocktails to ingredients

### User Data Tables
- **favorites**: User's favorite cocktails (session-based)
- **shopping_list**: User's shopping list items (session-based)
- **admin_sessions**: Admin authentication sessions

### Key Features
- ✅ Foreign key relationships ensure data integrity
- ✅ Indexes for optimal query performance
- ✅ Automatic timestamps with triggers
- ✅ Session-based user data (no authentication required)
- ✅ Support for all existing LocalStorage data structures

## 🔄 Migration Features

### Gradual Migration
- **Backward Compatible**: LocalStorage code still works
- **Feature Flag**: Easy switching between data sources
- **Fallback Support**: Automatic fallback to LocalStorage if Supabase fails

### Offline Support
- **Smart Caching**: Automatic caching of Supabase data
- **Offline Mode**: Full functionality when offline
- **Auto-Sync**: Automatic data sync when back online

### Error Handling
- **Retry Logic**: Automatic retry with exponential backoff
- **Graceful Degradation**: Fallback to cached/default data
- **User-Friendly Messages**: Clear error messages for users

## 🛠️ Architecture

### Data Access Layers
```
Application Layer
       ↓
Data Source Config (dataSource.ts)
       ↓
┌─────────────────┬─────────────────┐
│   LocalStorage  │    Supabase     │
│   (Original)    │    (New)        │
└─────────────────┴─────────────────┘
       ↓                   ↓
Resilient Data Service (with offline support)
```

### Key Components
- **`src/lib/supabase/`**: Supabase data access layer
- **`src/lib/offline/`**: Offline support and caching
- **`src/lib/error/`**: Error handling and retry logic
- **`src/lib/migration/`**: Data migration utilities
- **`src/config/dataSource.ts`**: Configuration and feature flags

## 🧪 Testing

### Automated Tests
```bash
# Run migration tests
npx ts-node src/scripts/testMigration.ts

# Test specific functionality
npm test # (if you have Jest/Vitest setup)
```

### Manual Testing Checklist
- [ ] Database schema created successfully
- [ ] Data migration completed without errors
- [ ] All cocktails display correctly
- [ ] Search functionality works
- [ ] Favorites can be added/removed
- [ ] Shopping list functions properly
- [ ] Admin panel operations work
- [ ] Offline mode functions correctly
- [ ] Data syncs when back online

## 🔧 Troubleshooting

### Common Issues

#### Migration Fails
```bash
# Check database connection
npx ts-node -e "import { supabase } from './src/lib/supabase'; supabase.from('ingredients').select('count').then(console.log)"

# Force migration
npx ts-node src/scripts/migrateData.ts --force
```

#### Data Not Loading
1. Check environment variables are set correctly
2. Verify Supabase URL and API key
3. Check browser console for errors
4. Ensure `NEXT_PUBLIC_USE_SUPABASE=true`

#### Offline Mode Issues
1. Clear browser cache and localStorage
2. Check if service worker is registered
3. Verify cache manager is working

### Debug Mode
Enable debug logging by adding to `.env.local`:
```env
NEXT_PUBLIC_DEBUG=true
```

## 📈 Performance Considerations

### Optimization Tips
- **Batch Operations**: Use bulk inserts for large datasets
- **Caching Strategy**: Implement smart caching for frequently accessed data
- **Connection Pooling**: Supabase handles this automatically
- **Query Optimization**: Use indexes and proper query patterns

### Monitoring
- Monitor Supabase dashboard for performance metrics
- Track cache hit rates in browser dev tools
- Monitor network requests and response times

## 🔒 Security

### Data Protection
- All data is encrypted in transit (HTTPS)
- Supabase provides encryption at rest
- Row Level Security (RLS) can be enabled for user data
- API keys are properly scoped (anon key for public access)

### Session Management
- Session-based user identification (no authentication required)
- Admin sessions stored securely in database
- Automatic session cleanup for expired sessions

## 🚀 Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database schema deployed
- [ ] Data migrated successfully
- [ ] Tests passing
- [ ] Error monitoring setup
- [ ] Performance monitoring enabled
- [ ] Backup strategy in place

### Environment Variables for Production
```env
NEXT_PUBLIC_SUPABASE_URL=your-production-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
NEXT_PUBLIC_USE_SUPABASE=true
```

## 📚 API Reference

### Supabase Utilities
```typescript
// Cocktails
import { getCocktails, getCocktailById, createCocktail } from '@/lib/supabase';

// Ingredients
import { getIngredients, createIngredient } from '@/lib/supabase';

// Favorites
import { getFavoriteIds, addFavorite, removeFavorite } from '@/lib/supabase';

// Shopping List
import { getShoppingList, addToShoppingList } from '@/lib/supabase';
```

### Resilient Data Service
```typescript
// Offline-capable data access
import {
  getResilientCocktails,
  getResilientIngredients,
  syncAllData
} from '@/lib/resilientDataService';
```

## 🤝 Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run database migrations
5. Start development server: `npm run dev`

### Making Changes
1. Update both LocalStorage and Supabase implementations
2. Add tests for new functionality
3. Update documentation
4. Test migration process

## 📞 Support

### Getting Help
- Check the troubleshooting section above
- Review Supabase documentation: https://supabase.com/docs
- Check browser console for error messages
- Verify environment configuration

### Known Limitations
- Session-based user data (no persistent user accounts)
- Limited to single-tenant usage
- Requires internet connection for full functionality

---

## 🎉 Migration Complete!

Once you've completed all steps:

1. ✅ Database schema is set up
2. ✅ Data has been migrated
3. ✅ Tests are passing
4. ✅ Application is using Supabase

Your CocktailFlow app is now powered by Supabase with offline support and robust error handling!
