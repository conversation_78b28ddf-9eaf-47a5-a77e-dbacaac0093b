'use client';

// Simple admin authentication for demo purposes
// In production, you'd want proper authentication with JWT, sessions, etc.

const ADMIN_KEY = 'cocktailflow-admin-auth';
const ADMIN_PASSWORD = 'cocktailflow2024'; // In production, this would be hashed and stored securely

export interface AdminSession {
  isAuthenticated: boolean;
  loginTime: number;
  expiresAt: number;
}

/**
 * Check if user is currently authenticated as admin
 */
export function isAdminAuthenticated(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    const session = localStorage.getItem(ADMIN_KEY);
    if (!session) return false;
    
    const adminSession: AdminSession = JSON.parse(session);
    
    // Check if session is expired (24 hours)
    if (Date.now() > adminSession.expiresAt) {
      localStorage.removeItem(ADMIN_KEY);
      return false;
    }
    
    return adminSession.isAuthenticated;
  } catch (error) {
    console.error('Error checking admin authentication:', error);
    return false;
  }
}

/**
 * Authenticate admin with password
 */
export function authenticateAdmin(password: string): boolean {
  if (password !== ADMIN_PASSWORD) {
    return false;
  }
  
  const session: AdminSession = {
    isAuthenticated: true,
    loginTime: Date.now(),
    expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
  };
  
  try {
    localStorage.setItem(ADMIN_KEY, JSON.stringify(session));
    return true;
  } catch (error) {
    console.error('Error storing admin session:', error);
    return false;
  }
}

/**
 * Logout admin
 */
export function logoutAdmin(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(ADMIN_KEY);
  } catch (error) {
    console.error('Error logging out admin:', error);
  }
}

/**
 * Get admin session info
 */
export function getAdminSession(): AdminSession | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const session = localStorage.getItem(ADMIN_KEY);
    if (!session) return null;
    
    return JSON.parse(session);
  } catch (error) {
    console.error('Error getting admin session:', error);
    return null;
  }
}
