'use client';

/**
 * Utilities for managing favorite cocktails using localStorage
 */

const FAVORITES_KEY = 'cocktailflow-favorites';

/**
 * Get all favorite cocktail IDs from localStorage
 */
export function getFavoriteIds(): string[] {
  if (typeof window === 'undefined') return [];
  
  try {
    const favorites = localStorage.getItem(FAVORITES_KEY);
    return favorites ? JSON.parse(favorites) : [];
  } catch (error) {
    console.error('Error reading favorites from localStorage:', error);
    return [];
  }
}

/**
 * Check if a cocktail is favorited
 */
export function isFavorite(cocktailId: string): boolean {
  const favorites = getFavoriteIds();
  return favorites.includes(cocktailId);
}

/**
 * Add a cocktail to favorites
 */
export function addToFavorites(cocktailId: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    const favorites = getFavoriteIds();
    if (!favorites.includes(cocktailId)) {
      favorites.push(cocktailId);
      localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites));
    }
  } catch (error) {
    console.error('Error adding to favorites:', error);
  }
}

/**
 * Remove a cocktail from favorites
 */
export function removeFromFavorites(cocktailId: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    const favorites = getFavoriteIds();
    const updatedFavorites = favorites.filter(id => id !== cocktailId);
    localStorage.setItem(FAVORITES_KEY, JSON.stringify(updatedFavorites));
  } catch (error) {
    console.error('Error removing from favorites:', error);
  }
}

/**
 * Toggle favorite status of a cocktail
 */
export function toggleFavorite(cocktailId: string): boolean {
  const isCurrentlyFavorite = isFavorite(cocktailId);
  
  if (isCurrentlyFavorite) {
    removeFromFavorites(cocktailId);
    return false;
  } else {
    addToFavorites(cocktailId);
    return true;
  }
}

/**
 * Clear all favorites
 */
export function clearAllFavorites(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(FAVORITES_KEY);
  } catch (error) {
    console.error('Error clearing favorites:', error);
  }
}

/**
 * Get count of favorite cocktails
 */
export function getFavoritesCount(): number {
  return getFavoriteIds().length;
}
