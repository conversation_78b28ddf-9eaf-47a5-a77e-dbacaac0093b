'use client';

import { Cocktail, Ingredient } from '@/types/cocktail';

export interface ShoppingListItem {
  ingredient: Ingredient;
  amount: string;
  cocktails: string[]; // cocktail names that use this ingredient
}

const SHOPPING_LIST_KEY = 'cocktailflow-shopping-list';

/**
 * Get shopping list from localStorage
 */
export function getShoppingList(): ShoppingListItem[] {
  if (typeof window === 'undefined') return [];
  
  try {
    const list = localStorage.getItem(SHOPPING_LIST_KEY);
    return list ? JSON.parse(list) : [];
  } catch (error) {
    console.error('Error reading shopping list from localStorage:', error);
    return [];
  }
}

/**
 * Save shopping list to localStorage
 */
export function saveShoppingList(items: ShoppingListItem[]): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(SHOPPING_LIST_KEY, JSON.stringify(items));
  } catch (error) {
    console.error('Error saving shopping list to localStorage:', error);
  }
}

/**
 * Add cocktail ingredients to shopping list
 */
export function addCocktailToShoppingList(cocktail: Cocktail): void {
  const currentList = getShoppingList();
  
  cocktail.ingredients.forEach(({ ingredient, amount }) => {
    // Skip garnishes for shopping list
    if (ingredient.category === 'garnish') return;
    
    const existingItem = currentList.find(item => item.ingredient.id === ingredient.id);
    
    if (existingItem) {
      // Add cocktail name if not already included
      if (!existingItem.cocktails.includes(cocktail.name)) {
        existingItem.cocktails.push(cocktail.name);
      }
    } else {
      // Add new item
      currentList.push({
        ingredient,
        amount,
        cocktails: [cocktail.name]
      });
    }
  });
  
  saveShoppingList(currentList);
}

/**
 * Remove ingredient from shopping list
 */
export function removeFromShoppingList(ingredientId: string): void {
  const currentList = getShoppingList();
  const updatedList = currentList.filter(item => item.ingredient.id !== ingredientId);
  saveShoppingList(updatedList);
}

/**
 * Clear entire shopping list
 */
export function clearShoppingList(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(SHOPPING_LIST_KEY);
  } catch (error) {
    console.error('Error clearing shopping list:', error);
  }
}

/**
 * Generate shopping list from multiple cocktails
 */
export function generateShoppingListFromCocktails(cocktails: Cocktail[]): ShoppingListItem[] {
  const ingredientMap = new Map<string, ShoppingListItem>();
  
  cocktails.forEach(cocktail => {
    cocktail.ingredients.forEach(({ ingredient, amount }) => {
      // Skip garnishes for shopping list
      if (ingredient.category === 'garnish') return;
      
      const existing = ingredientMap.get(ingredient.id);
      
      if (existing) {
        // Add cocktail name if not already included
        if (!existing.cocktails.includes(cocktail.name)) {
          existing.cocktails.push(cocktail.name);
        }
      } else {
        // Add new ingredient
        ingredientMap.set(ingredient.id, {
          ingredient,
          amount,
          cocktails: [cocktail.name]
        });
      }
    });
  });
  
  return Array.from(ingredientMap.values());
}

/**
 * Get shopping list count
 */
export function getShoppingListCount(): number {
  return getShoppingList().length;
}
