'use client';

import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { cocktails as initialCocktails } from '@/data/cocktails';
import { ingredients as initialIngredients, glassTypes as initialGlassTypes } from '@/data/ingredients';

// Keys for localStorage
const ADMIN_COCKTAILS_KEY = 'cocktailflow-admin-cocktails';
const ADMIN_INGREDIENTS_KEY = 'cocktailflow-admin-ingredients';
const ADMIN_GLASS_TYPES_KEY = 'cocktailflow-admin-glass-types';

/**
 * Get all cocktails (from localStorage or default data)
 */
export function getAdminCocktails(): Cocktail[] {
  if (typeof window === 'undefined') return initialCocktails;
  
  try {
    const stored = localStorage.getItem(ADMIN_COCKTAILS_KEY);
    return stored ? JSON.parse(stored) : initialCocktails;
  } catch (error) {
    console.error('Error loading admin cocktails:', error);
    return initialCocktails;
  }
}

/**
 * Save cocktails to localStorage
 */
export function saveAdminCocktails(cocktails: Cocktail[]): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(ADMIN_COCKTAILS_KEY, JSON.stringify(cocktails));
  } catch (error) {
    console.error('Error saving admin cocktails:', error);
  }
}

/**
 * Add a new cocktail
 */
export function addCocktail(cocktail: Cocktail): boolean {
  try {
    const cocktails = getAdminCocktails();
    
    // Check if ID already exists
    if (cocktails.find(c => c.id === cocktail.id)) {
      throw new Error('Cocktail with this ID already exists');
    }
    
    cocktails.push(cocktail);
    saveAdminCocktails(cocktails);
    return true;
  } catch (error) {
    console.error('Error adding cocktail:', error);
    return false;
  }
}

/**
 * Update an existing cocktail
 */
export function updateCocktail(cocktailId: string, updatedCocktail: Cocktail): boolean {
  try {
    const cocktails = getAdminCocktails();
    const index = cocktails.findIndex(c => c.id === cocktailId);
    
    if (index === -1) {
      throw new Error('Cocktail not found');
    }
    
    cocktails[index] = updatedCocktail;
    saveAdminCocktails(cocktails);
    return true;
  } catch (error) {
    console.error('Error updating cocktail:', error);
    return false;
  }
}

/**
 * Delete a cocktail
 */
export function deleteCocktail(cocktailId: string): boolean {
  try {
    const cocktails = getAdminCocktails();
    const filteredCocktails = cocktails.filter(c => c.id !== cocktailId);
    
    if (filteredCocktails.length === cocktails.length) {
      throw new Error('Cocktail not found');
    }
    
    saveAdminCocktails(filteredCocktails);
    return true;
  } catch (error) {
    console.error('Error deleting cocktail:', error);
    return false;
  }
}

/**
 * Get all ingredients (from localStorage or default data)
 */
export function getAdminIngredients(): Ingredient[] {
  if (typeof window === 'undefined') return initialIngredients;
  
  try {
    const stored = localStorage.getItem(ADMIN_INGREDIENTS_KEY);
    return stored ? JSON.parse(stored) : initialIngredients;
  } catch (error) {
    console.error('Error loading admin ingredients:', error);
    return initialIngredients;
  }
}

/**
 * Save ingredients to localStorage
 */
export function saveAdminIngredients(ingredients: Ingredient[]): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(ADMIN_INGREDIENTS_KEY, JSON.stringify(ingredients));
  } catch (error) {
    console.error('Error saving admin ingredients:', error);
  }
}

/**
 * Add a new ingredient
 */
export function addIngredient(ingredient: Ingredient): boolean {
  try {
    const ingredients = getAdminIngredients();
    
    // Check if ID already exists
    if (ingredients.find(i => i.id === ingredient.id)) {
      throw new Error('Ingredient with this ID already exists');
    }
    
    ingredients.push(ingredient);
    saveAdminIngredients(ingredients);
    return true;
  } catch (error) {
    console.error('Error adding ingredient:', error);
    return false;
  }
}

/**
 * Update an existing ingredient
 */
export function updateIngredient(ingredientId: string, updatedIngredient: Ingredient): boolean {
  try {
    const ingredients = getAdminIngredients();
    const index = ingredients.findIndex(i => i.id === ingredientId);
    
    if (index === -1) {
      throw new Error('Ingredient not found');
    }
    
    ingredients[index] = updatedIngredient;
    saveAdminIngredients(ingredients);
    return true;
  } catch (error) {
    console.error('Error updating ingredient:', error);
    return false;
  }
}

/**
 * Delete an ingredient
 */
export function deleteIngredient(ingredientId: string): boolean {
  try {
    const ingredients = getAdminIngredients();
    const filteredIngredients = ingredients.filter(i => i.id !== ingredientId);
    
    if (filteredIngredients.length === ingredients.length) {
      throw new Error('Ingredient not found');
    }
    
    saveAdminIngredients(filteredIngredients);
    return true;
  } catch (error) {
    console.error('Error deleting ingredient:', error);
    return false;
  }
}

/**
 * Get all glass types (from localStorage or default data)
 */
export function getAdminGlassTypes(): GlassType[] {
  if (typeof window === 'undefined') return initialGlassTypes;
  
  try {
    const stored = localStorage.getItem(ADMIN_GLASS_TYPES_KEY);
    return stored ? JSON.parse(stored) : initialGlassTypes;
  } catch (error) {
    console.error('Error loading admin glass types:', error);
    return initialGlassTypes;
  }
}

/**
 * Save glass types to localStorage
 */
export function saveAdminGlassTypes(glassTypes: GlassType[]): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(ADMIN_GLASS_TYPES_KEY, JSON.stringify(glassTypes));
  } catch (error) {
    console.error('Error saving admin glass types:', error);
  }
}

/**
 * Generate a unique ID for new items
 */
export function generateId(prefix: string = ''): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `${prefix}${prefix ? '-' : ''}${timestamp}-${random}`;
}

/**
 * Reset all data to defaults
 */
export function resetToDefaults(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(ADMIN_COCKTAILS_KEY);
    localStorage.removeItem(ADMIN_INGREDIENTS_KEY);
    localStorage.removeItem(ADMIN_GLASS_TYPES_KEY);
  } catch (error) {
    console.error('Error resetting to defaults:', error);
  }
}
