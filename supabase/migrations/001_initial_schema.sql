-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create ingredients table
CREATE TABLE ingredients (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('spirit', 'liqueur', 'mixer', 'juice', 'syrup', 'bitters', 'garnish', 'other')),
  alcoholic BOOLEAN NOT NULL DEFAULT false,
  description TEXT,
  abv NUMERIC(5,2), -- Alcohol by volume percentage
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create glass_types table
CREATE TABLE glass_types (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  icon_url TEXT,
  capacity TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cocktails table
CREATE TABLE cocktails (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  instructions TEXT[] NOT NULL,
  glass_type_id TEXT NOT NULL REFERENCES glass_types(id),
  category TEXT NOT NULL CHECK (category IN ('classic', 'modern', 'tropical', 'sour', 'sweet', 'bitter', 'strong', 'refreshing', 'creamy', 'hot', 'frozen')),
  tags TEXT[] DEFAULT '{}',
  difficulty TEXT NOT NULL CHECK (difficulty IN ('easy', 'medium', 'hard')),
  prep_time INTEGER NOT NULL DEFAULT 0,
  servings INTEGER NOT NULL DEFAULT 1,
  image_url TEXT,
  garnish TEXT,
  history TEXT,
  variations TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cocktail_ingredients junction table
CREATE TABLE cocktail_ingredients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  cocktail_id TEXT NOT NULL REFERENCES cocktails(id) ON DELETE CASCADE,
  ingredient_id TEXT NOT NULL REFERENCES ingredients(id),
  amount TEXT NOT NULL,
  optional BOOLEAN NOT NULL DEFAULT false,
  garnish BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(cocktail_id, ingredient_id)
);

-- Create favorites table (using session-based approach for now)
CREATE TABLE favorites (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  cocktail_id TEXT NOT NULL REFERENCES cocktails(id) ON DELETE CASCADE,
  user_session TEXT NOT NULL, -- Session identifier for anonymous users
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(cocktail_id, user_session)
);

-- Create shopping_list table
CREATE TABLE shopping_list (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ingredient_id TEXT NOT NULL REFERENCES ingredients(id),
  amount TEXT NOT NULL,
  cocktail_names TEXT[] DEFAULT '{}',
  user_session TEXT NOT NULL, -- Session identifier for anonymous users
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(ingredient_id, user_session)
);

-- Create admin_sessions table
CREATE TABLE admin_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_token TEXT NOT NULL UNIQUE,
  is_authenticated BOOLEAN NOT NULL DEFAULT true,
  login_time TIMESTAMP WITH TIME ZONE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_cocktails_category ON cocktails(category);
CREATE INDEX idx_cocktails_difficulty ON cocktails(difficulty);
CREATE INDEX idx_cocktails_glass_type ON cocktails(glass_type_id);
CREATE INDEX idx_cocktail_ingredients_cocktail ON cocktail_ingredients(cocktail_id);
CREATE INDEX idx_cocktail_ingredients_ingredient ON cocktail_ingredients(ingredient_id);
CREATE INDEX idx_favorites_user_session ON favorites(user_session);
CREATE INDEX idx_shopping_list_user_session ON shopping_list(user_session);
CREATE INDEX idx_admin_sessions_token ON admin_sessions(session_token);
CREATE INDEX idx_admin_sessions_expires ON admin_sessions(expires_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_ingredients_updated_at BEFORE UPDATE ON ingredients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_glass_types_updated_at BEFORE UPDATE ON glass_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cocktails_updated_at BEFORE UPDATE ON cocktails FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shopping_list_updated_at BEFORE UPDATE ON shopping_list FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_admin_sessions_updated_at BEFORE UPDATE ON admin_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
